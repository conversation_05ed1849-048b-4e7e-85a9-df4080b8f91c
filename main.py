import json

from modules.application import Application
from modules.fb_session import FBSession
from modules.globals import load_app_config
from modules.mfb_session import MFBSession


# <EMAIL>:Jim80527

# https://m.facebook.com/help/147926301947841

# k form
# <EMAIL>:Gust4v0



def main():
    config = load_app_config()
    proxy_config = config.get("proxy", {})
    # username, password = "<EMAIL>|Interthi1".split("|")
    # username, password = "<EMAIL>|CsMB7.54CX!cC75".split("|")
    # username, password = "<EMAIL>:newfiesta2012".split(":")
    username, password = "<EMAIL>:luna1506".split(":")
    # username, password = "100010012035422|Ahihi123@@".split("|")
    # username, password = "<EMAIL>:H4jrf57stav".split(":")
    with FBSession(proxy_config, username, password) as session:
        result = session.login()
        if result.get('status'):
            print("Login successful")
            pages = session.count_pages()
            print(pages)
            html = session.check_chat_us()
            print(html)
        else:
            print(f"Login failed, reason: {result.get('type')}")
            # pages = session.count_pages()
            # print(pages)

        # result = session.m_login()
        # print(result)
        # if result.get("Status"):
        #     session.update_header_to_web()
        #     pages = session.count_pages()
        #     print(pages)
        # else:
        #     print(f"Login failed, reason: {result.get('Type')}")


if __name__ == "__main__":
    # main()
    app = Application()
    app.run()


    # with open("test.json", "rb") as f:
    #     j = json.load(f)
    #     print(json.dumps(j, indent=4))
