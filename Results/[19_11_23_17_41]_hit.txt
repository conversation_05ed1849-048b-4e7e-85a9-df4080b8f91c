<EMAIL>:mohamedmakan99|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=***********&cookie=\u00257B\u002522u\u002522\u00253A***************\u00252C\u002522t\u002522\u00253A1732033097\u00252C\u002522step\u002522\u00253A2\u00252C\u002522n\u002522\u00253A\u002522z4tSO22053Q\u00253D\u002522\u00252C\u002522inst\u002522\u00253A174214303362884\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBFAiEAzt3xAaRp0uIj0Wf44FYubrphVpunaZH1w3rbVYDZH1ACICMBl25MVLiNKbyvPWqnb2pX6JO53vnaUtTa9E5k-fGS\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWVrSaHVPEFkx87-Fz4\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522Sbo8Z-w7c4yuA-baHSJSXg-U\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWWNX7eQNcrLOk4MDpE","flow_id":***************,"uid":***************,"show_native_checkpoints":false,"start_internal_webview_from_url":true,"positive_button_string":"Get Started","machine_id":"Sbo8Z-w7c4yuA-baHSJSXg-U"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AhSjdBiPgLGY5g4iiNWs1hr"}}
<EMAIL>:insomniac1|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=**************&cookie=\u00257B\u002522u\u002522\u00253A***************\u00252C\u002522t\u002522\u00253A1732033140\u00252C\u002522step\u002522\u00253A5\u00252C\u002522n\u002522\u00253A\u002522f3Y0qkdPgT0\u00253D\u002522\u00252C\u002522inst\u002522\u00253A956216417728699\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBFAiEAoHS0DlAePUnDqUN8egn7CQMtIG9MbQYTq3ziVnQ4_4YCIDdchbeFiHqFlVCuv83yZ7CqASbzsBJjEdGViyYDwR6z\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWVTiqIX_Ie6s41YoQI\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522dLo8Zyn82fPPxM-9RmSaaazA\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWVbIrypT1-sF7UKIBk","flow_id":***************,"uid":***************,"show_native_checkpoints":false,"start_internal_webview_from_url":true,"positive_button_string":"Get Started","machine_id":"dLo8Zyn82fPPxM-9RmSaaazA"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"ATztUk7MSIkQen8x_m0CMrv"}}
<EMAIL>:isa2015fr|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=***************&cookie=\u00257B\u002522u\u002522\u00253A***************\u00252C\u002522t\u002522\u00253A1732034750\u00252C\u002522step\u002522\u00253A0\u00252C\u002522n\u002522\u00253A\u002522yZXm0gk7rHw\u00253D\u002522\u00252C\u002522inst\u002522\u00253A310681278731560\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBFAiAWo8cLjQHFzID_IMQByfqDwRirX5IvCAtDfuBZHbW3FAIhAIyRIRpj3Lgo2HcLynnTDgbjHOIN9oZD_NsxnVlYIQpQ\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWUACcQ1-ONrRaEQO5g\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522vsA8Z8gMhsVO_A-_TIh0x4NN\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWWFIZODHEL9_paBhps","flow_id":***************,"uid":***************,"show_native_checkpoints":false,"start_internal_webview_from_url":true,"positive_button_string":"Get Started","machine_id":"vsA8Z8gMhsVO_A-_TIh0x4NN"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AO1hvy7fWXXJhs-FwHfPvR7"}}
<EMAIL>:86250353Whs@|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=*************&cookie=\u00257B\u002522u\u002522\u00253A100003272306404\u00252C\u002522t\u002522\u00253A1732035248\u00252C\u002522step\u002522\u00253A0\u00252C\u002522n\u002522\u00253A\u002522i2R32iZV0SI\u00253D\u002522\u00252C\u002522inst\u002522\u00253A6562324247219920\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBGAiEAz_UPOmdZLVA-wPhEQtVJCEC-ySYspaM2mqqiMsdH7bUCIQCXfDwVZJrPrKQbZ68l18iMpU71DYaJ8jqUDrwUO_iZQw\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWW5TZInJi4BQ-mOTVM\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522sMI8Z1YPL7_ehhhJpeAaM0ph\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWWNbJYc9fjvsMc8Fww","flow_id":***************,"start_internal_webview_from_url":true,"show_native_checkpoints":false,"machine_id":"sMI8Z1YPL7_ehhhJpeAaM0ph"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AIEyItIBcWWfrjLwSl2qCoy"}}
<EMAIL>:ramjanki@1212|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=**************&cookie=\u00257B\u002522u\u002522\u00253A***************\u00252C\u002522t\u002522\u00253A1732036375\u00252C\u002522step\u002522\u00253A0\u00252C\u002522n\u002522\u00253A\u00252201akK5\u00252BL5YI\u00253D\u002522\u00252C\u002522inst\u002522\u00253A5451394721642995\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBGAiEA2dl0AxrpnKSBNBiTWbJVysilQGShXx9pNKDOgYVK29MCIQCf9EQ0CvDyWL8nxv91ByK9TfuWyuCS0BFaCTVFNVTElg\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWXujUy7O4PzDNQrq30\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522F8c8Z3u3Eg9SvbLzeDCj29HO\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWXbEKwl9xEWkAyWzTY","flow_id":***************,"uid":***************,"show_native_checkpoints":false,"start_internal_webview_from_url":true,"positive_button_string":"Get Started","machine_id":"F8c8Z3u3Eg9SvbLzeDCj29HO"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AKPOMGnj_mILxWTr-ycY_TD"}}
<EMAIL>:T-hg5qPrLR49_er|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=*************&cookie=\u00257B\u002522u\u002522\u00253A100003482463165\u00252C\u002522t\u002522\u00253A1732036758\u00252C\u002522step\u002522\u00253A0\u00252C\u002522n\u002522\u00253A\u002522uvSq\u00255C\u00252F4flmRw\u00253D\u002522\u00252C\u002522inst\u002522\u00253A6478249035634448\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBGAiEA9_5zA13Kt7WGeLioszmawR9XrnHkZMC5YFfZndN2jRACIQDGLZk8hFK1-9xWh6UEq1ZyCr-lh_Mz1o0Knpf094Tkmg\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWX0DS991mtt-9xTufk\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522lsg8Z9wJ8xjtJCyxlmmXhKK8\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWXCFh5I1JvK-4mjm04","flow_id":***************,"start_internal_webview_from_url":true,"show_native_checkpoints":false,"machine_id":"lsg8Z9wJ8xjtJCyxlmmXhKK8"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AVTp13x47WS5DFwb3zZT9eD"}}
<EMAIL>:tarun1024shetty|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=***************&cookie=\u00257B\u002522u\u002522\u00253A100069544830260\u00252C\u002522t\u002522\u00253A1732036818\u00252C\u002522step\u002522\u00253A0\u00252C\u002522n\u002522\u00253A\u002522A3ngxLI7jGE\u00253D\u002522\u00252C\u002522inst\u002522\u00253A642114398116716\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBFAiEA9nrRy0Q8yxpDQT35TrGkf9oMn1XMgaqUjXAF-H1sM1ACIFiLNmFe7gIpVCE2aYYiJrTu3BCAFrFiuu5cDQxqEm1z\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWXi7c2IbzkGAiWJX2s\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u0025220sg8ZzvZDXO4LR-S1kvD0ezg\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWUwOBRSDHWbyTRV_DM","flow_id":***************,"start_internal_webview_from_url":true,"show_native_checkpoints":false,"machine_id":"0sg8ZzvZDXO4LR-S1kvD0ezg"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AGlzwFhV3VBvkEbuODKmY4d"}}
<EMAIL>:BLACHKA2021|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=*************&cookie=\u00257B\u002522u\u002522\u00253A100066956516890\u00252C\u002522t\u002522\u00253A1732036950\u00252C\u002522step\u002522\u00253A0\u00252C\u002522n\u002522\u00253A\u002522cJZpzOi7hpA\u00253D\u002522\u00252C\u002522inst\u002522\u00253A133425502232666\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBFAiBYZJBgO5tAkClepG_qRxI9DtMWEgy-hSFB4VxQkponFgIhAMQ71CwaLHE92EuQ2RNyBDWvhWSrY4aWybNDhDd0rF5n\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWUeTuDUICIH-2FdXuw\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522Vsk8Z9LAWjTslgzbidGlhQa-\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWWE-yj5K7fYNHQN-Fk","flow_id":***************,"start_internal_webview_from_url":true,"show_native_checkpoints":false,"machine_id":"Vsk8Z9LAWjTslgzbidGlhQa-"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AMAIRCoVnLOhY2uDWzsIfj2"}}
<EMAIL>:fURRINA2016|Unknown|{"error":{"message":"User must verify their account on www.facebook.com","type":"OAuthException","code":405,"error_data":{"url":"https:\/\/www.facebook.com\/checkpoint\/start\/?ip=*************&cookie=\u00257B\u002522u\u002522\u00253A***************\u00252C\u002522t\u002522\u00253A1732037507\u00252C\u002522step\u002522\u00253A5\u00252C\u002522n\u002522\u00253A\u002522TnwvJ8N6SmQ\u00253D\u002522\u00252C\u002522inst\u002522\u00253A123726568018587\u00252C\u002522f\u002522\u00253A***************\u00252C\u002522st\u002522\u00253A\u002522p\u002522\u00252C\u002522aid\u002522\u00253Anull\u00252C\u002522ca\u002522\u00253Anull\u00252C\u002522la\u002522\u00253A\u002522\u002522\u00252C\u002522ta\u002522\u00253A\u0025************.ch.s\u00253Apw.tDBFAiEAtbL7ftBeP52z_bcsQkx11P7gbMGRMUk_T9CJtGds5egCIE2Jota4Nf3wPjbHe7GZj4fFW6H1qA9uulsykLsFmVIP\u002522\u00252C\u002522tfvaid\u002522\u00253Anull\u00252C\u002522tfvasec\u002522\u00253Anull\u00252C\u002522sat\u002522\u00253Anull\u00252C\u002522idg\u002522\u00253Afalse\u00252C\u002522cidue\u002522\u00253A\u002522\u002522\u00252C\u002522tfuln\u002522\u00253Anull\u00252C\u002522tfvri\u002522\u00253Anull\u00252C\u002522ct\u002522\u00253Anull\u00252C\u002522s\u002522\u00253A\u002522AWW-iB_qNP_uDFdnkEU\u002522\u00252C\u002522cs\u002522\u00253A\u00255B\u00255D\u00252C\u002522dc\u002522\u00253A\u002522g8s8Z_V8oM8YuPj0-r24d5J0\u002522\u00257D&next=fb\u00253A\u00252F\u00252Ffeed\u00252F&hash=AWWSnraEMNgl1cSvpZQ","flow_id":***************,"uid":***************,"show_native_checkpoints":false,"start_internal_webview_from_url":true,"positive_button_string":"Get Started","machine_id":"g8s8Z_V8oM8YuPj0-r24d5J0"},"error_subcode":1348093,"is_transient":false,"error_user_title":"Confirm Your Identity","error_user_msg":"To log into your Facebook account, you need to first confirm your identity.","fbtrace_id":"AjtzQCcAQ0swxSnkaeSHsA8"}}
