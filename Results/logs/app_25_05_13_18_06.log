2025-05-25 13:18:06,532 - MainThread - INFO - Application MFB v1.0.0 started
2025-05-25 13:18:06,532 - MainThread - INFO - Log directory: Results\logs
2025-05-25 13:18:06,532 - MainThread - INFO - Result directory: Results\Results
2025-05-25 13:18:15,432 - Thread-74 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate (_ssl.c:1006)')': /login.php
2025-05-25 13:18:21,148 - Thread-20 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:21,711 - Thread-85 (worker) - INFO - Unknown redirect url: https://www.facebook.com/recover/initiate/?lara_product=www_first_password_failure
2025-05-25 13:18:22,235 - Thread-95 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:22,247 - Thread-87 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:22,422 - Thread-44 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:23,832 - Thread-7 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,383 - Thread-25 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,477 - Thread-8 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,486 - Thread-33 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,500 - Thread-17 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,520 - Thread-86 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,525 - Thread-42 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,539 - Thread-69 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,557 - Thread-93 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,559 - Thread-98 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:25,567 - Thread-94 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:27,193 - Thread-87 (worker) - INFO - Unknown redirect url: https://www.facebook.com/recover/initiate/?lara_product=www_first_password_failure
2025-05-25 13:18:27,231 - Thread-5 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:27,416 - Thread-27 (worker) - INFO - Unknown redirect url: https://www.facebook.com/recover/initiate/?lara_product=www_first_password_failure
2025-05-25 13:18:28,976 - Thread-71 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")': /login.php
2025-05-25 13:18:29,207 - Thread-89 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:29,419 - Thread-75 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:29,751 - Thread-71 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")': /login.php
2025-05-25 13:18:29,793 - Thread-51 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:30,488 - Thread-71 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")': /login.php
2025-05-25 13:18:30,849 - Thread-82 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:31,225 - Thread-71 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")': /login.php
2025-05-25 13:18:31,513 - Thread-96 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:31,953 - Thread-71 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")': /login.php
2025-05-25 13:18:32,190 - Thread-97 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:32,692 - Thread-71 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'www.facebook.com'. (_ssl.c:1006)")))
2025-05-25 13:18:33,295 - Thread-94 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:33,649 - Thread-81 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:34,012 - Thread-94 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:34,735 - Thread-5 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:35,034 - Thread-103 (worker) - INFO - Unknown redirect url: https://www.facebook.com/login/help.php?st=opw&lwv=120&lwc=1348060
2025-05-25 13:18:35,116 - Thread-92 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php?_rdc=1&_rdr
2025-05-25 13:18:35,161 - Thread-26 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:35,409 - Thread-32 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:35,812 - Thread-67 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:35,954 - Thread-45 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:36,306 - Thread-33 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:36,710 - Thread-89 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:36,876 - Thread-88 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:36,920 - Thread-75 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:36,996 - Thread-69 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:37,376 - Thread-91 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:37,995 - Thread-87 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:38,228 - Thread-27 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:39,914 - Thread-28 (worker) - INFO - Unknown redirect url: https://www.facebook.com/checkpoint/465803052217681/?next&ct=ARE0TI2YvhR1WmEtijPO3DUvSGZ6u4C95tohy4WX6306aWt2otaO8g-dwCUBkzWJs3ZdfJe1WlLKx1wsK3ciXKgF4qcBnitw9CgpEWiIwVURuMEI_fykg4J7S-ShH33Y5HcV76bwMqTPJQ24Mq9tYEqy61VpeX_tni-Ou5NfdhgnE2Klweo21WkXdg0RMgb1EQZfMCKcwcN3NJe-kz14RP4yshCGhXRwLqRlWXM8PUi3buGOfpz3rS2Yp5YMwvn62RkRyofonweP5swNpYmW2x0hSw5vxE32pVlDrQ6TNtpymihpke3QYAaWz00S6d9yFNmBmoCzFjpv5-iZkJ5E0XhFe1G29PeUDYGufUDxgVp2zB1C4vbjI8vyI64p93c8d1aP7iY-qam5jMez_-xGimCJRK04cQ&apc=AdkqYnoqd35R1YKu3-_I8sh0-4nFBL5gpZtfypCo-2YkhGJLAJGpdZ74uHxMqEkF0TYb1dRGdmlfq5GGhu9lTSk2Xic6-YtWc3gP8rDyLF7QHB6WuWWH1pKkP-huJYX9VDo2AAm-CobXRJDdzC9XhJveUXviBz2XxgtOPf4apFJ5FW2dMM2epk6zvzoM7ePLsbvB_jCe5X718x4Lfa1XhD1QF9VzN9lmeIbVjAKzpl-MikPey2FI0QQHnDoyQqUVS58Z1ZA0zxVM3bdlUTtq9sISnH5VFCdSOid8j_rZpybbNyZq6Sf3laMzhfArA91CYVNmnB953tiSzFibwfiZ4lXik20T5Vx_mvbjZBenOhM5gK-QYdB7Itym47Urf8MUy-JzdygsH07wjGVKRuaqQBKM0bW5HCO89fagRXO0e3tSs_Zsi1TLl5PSQQzU--Gog1-y1M6rLm2DE8WJ-pPWQE9HbV_pvqnKIzLY-3VhWv4oITlGXODNWE6j5h7FQ7KAloD8Fm0oLFmJGYmhgT_CDPtl3I4YX9AZRoJ9H20UZBhljHY1VQ61qiJU-OpaQqJz83Le6FWT1cVu3MGlMxCgWchYu7Zq7JSXdAm_f86driuc4a58FfZ7q0GrhDLd7_KVbqEn5_ysDlsZELzAAqptrjCTw53KBnvWO3e3Jg6qSmEHN9dUHfUb5_yw1zdpE9lRsG_E-gFldVQ9ohXKVR6oKxH31tbAtA-aZ-i-PIFkLP9Kr_y5ELkrkQ5boAWKqsMLCx1deh5EcMM4wQbpJIqMe0f-k5ZIPbqMgfdcZ8OJhliCU6ViY224AMPvWh4f7KDiNBDZp542mMbiICAlYmfzhQwrmmsrt1pRs_Q9G8Ui9WPuquRcJYwskODGsUFreKEpiKD9GX7nGPrmUU6SMkLZl1iSPwzfYO2UmD2GItSCzZTq9FKCAEg27ud1Xp66B7MsuylTQcDgDglGhYwy%7Caplc
2025-05-25 13:18:39,992 - Thread-70 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:40,118 - Thread-76 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:40,321 - Thread-48 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:40,332 - Thread-6 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:40,419 - Thread-59 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:40,524 - Thread-65 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:40,610 - Thread-51 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:41,158 - Thread-81 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:41,273 - Thread-57 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:42,133 - Thread-65 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:42,146 - Thread-23 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:42,243 - Thread-5 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:42,686 - Thread-26 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:42,864 - Thread-23 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:43,400 - Thread-12 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:43,448 - Thread-45 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:43,472 - Thread-44 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:43,580 - Thread-23 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:43,690 - Thread-65 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:43,875 - Thread-40 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,092 - Thread-50 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,209 - Thread-89 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,312 - Thread-96 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,320 - Thread-23 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,424 - Thread-75 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,492 - Thread-21 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,497 - Thread-69 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,876 - Thread-91 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:44,878 - Thread-39 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:45,070 - Thread-23 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:45,120 - Thread-30 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:45,194 - Thread-34 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:45,273 - Thread-65 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:45,790 - Thread-23 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:18:46,132 - Thread-10 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:46,457 - Thread-93 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:46,620 - Thread-67 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:46,874 - Thread-65 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:46,913 - Thread-58 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:47,125 - Thread-15 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:47,250 - Thread-43 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:47,683 - Thread-88 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:47,925 - Thread-59 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:48,488 - Thread-65 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:18:48,659 - Thread-81 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:49,202 - Thread-9 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:49,555 - Thread-68 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:49,747 - Thread-5 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:49,941 - Thread-77 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:50,186 - Thread-26 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:50,411 - Thread-102 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:50,579 - Thread-46 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:50,815 - Thread-70 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:50,926 - Thread-62 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:50,940 - Thread-45 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:51,147 - Thread-48 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:51,426 - Thread-51 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:51,699 - Thread-89 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:51,934 - Thread-75 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:51,990 - Thread-69 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:52,085 - Thread-57 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:52,110 - Thread-47 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:52,399 - Thread-91 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:52,725 - Thread-53 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:53,257 - Thread-37 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:54,621 - Thread-38 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:54,626 - Thread-79 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:54,698 - Thread-4 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:54,820 - Thread-15 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:54,902 - Thread-50 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:55,013 - Thread-11 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:55,118 - Thread-96 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:55,424 - Thread-59 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:56,177 - Thread-81 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:56,312 - Thread-74 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:18:56,887 - Thread-68 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:57,243 - Thread-5 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:57,259 - Thread-93 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:58,442 - Thread-45 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:58,501 - Thread-88 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:58,709 - Thread-84 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:59,204 - Thread-89 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:59,439 - Thread-75 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:59,508 - Thread-69 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:18:59,892 - Thread-91 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:00,128 - Thread-9 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,196 - Thread-98 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,308 - Thread-21 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,352 - Thread-97 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,521 - Thread-101 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:01,620 - Thread-13 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,622 - Thread-70 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,731 - Thread-62 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:01,946 - Thread-48 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,232 - Thread-51 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,267 - Thread-96 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,453 - Thread-72 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,541 - Thread-15 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,897 - Thread-57 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,927 - Thread-59 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:02,929 - Thread-47 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:03,304 - Thread-67 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')': /login.php
2025-05-25 13:19:03,677 - Thread-81 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:04,036 - Thread-67 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')': /login.php
2025-05-25 13:19:04,052 - Thread-37 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:04,617 - Thread-35 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:04,747 - Thread-5 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:04,783 - Thread-67 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')': /login.php
2025-05-25 13:19:05,061 - Thread-22 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:05,433 - Thread-79 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:05,508 - Thread-67 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')': /login.php
2025-05-25 13:19:05,947 - Thread-45 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:06,085 - Thread-14 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:06,243 - Thread-67 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')': /login.php
2025-05-25 13:19:06,593 - Thread-36 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:06,632 - Thread-54 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:06,707 - Thread-89 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:06,733 - Thread-8 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:06,933 - Thread-75 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:06,978 - Thread-67 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1006)')))
2025-05-25 13:19:07,006 - Thread-69 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:07,381 - Thread-91 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:07,762 - Thread-103 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php?_rdc=1&_rdr
2025-05-25 13:19:07,787 - Thread-44 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:08,087 - Thread-93 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:08,135 - Thread-80 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:08,173 - Thread-78 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:09,237 - Thread-52 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:09,302 - Thread-88 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:09,946 - Thread-72 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:10,251 - Thread-15 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:10,374 - Thread-28 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:10,415 - Thread-40 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:10,430 - Thread-59 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:10,797 - Thread-83 (worker) - ERROR - Error in post
Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 737, in _error_catcher
    yield
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1187, in read_chunked
    chunk = self._handle_chunk(amt)
            ^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1129, in _handle_chunk
    value = self._fp._safe_read(amt)  # type: ignore[union-attr]
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\http\client.py", line 631, in _safe_read
    data = self.fp.read(amt)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 706, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\ssl.py", line 1311, in recv_into
    return self.read(nbytes, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\ssl.py", line 1167, in read
    return self._sslobj.read(len, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1040, in stream
    yield from self.read_chunked(amt, decode_content=decode_content)
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1172, in read_chunked
    with self._error_catcher():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 742, in _error_catcher
    raise ReadTimeoutError(self._pool, None, "Read timed out.") from e  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 746, in send
    r.content
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\models.py", line 826, in generate
    raise ConnectionError(e)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out.
2025-05-25 13:19:11,144 - Thread-40 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:11,182 - Thread-81 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:11,551 - Thread-12 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:11,900 - Thread-40 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:11,946 - Thread-52 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:11,995 - Thread-98 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:12,192 - Thread-97 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:12,370 - Thread-33 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:12,463 - Thread-70 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:12,561 - Thread-62 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:12,634 - Thread-40 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:12,758 - Thread-48 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:12,853 - Thread-29 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:13,065 - Thread-51 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:13,374 - Thread-40 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:13,405 - Thread-12 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:13,449 - Thread-45 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:13,726 - Thread-57 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:13,739 - Thread-47 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:13,927 - Thread-63 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:14,093 - Thread-40 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:14,520 - Thread-69 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:14,821 - Thread-58 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:14,854 - Thread-37 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:14,878 - Thread-91 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:15,203 - Thread-12 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:15,292 - Thread-44 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:15,430 - Thread-26 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:15,632 - Thread-5 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:16,007 - Thread-25 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:16,239 - Thread-79 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:16,269 - Thread-56 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:16,402 - Thread-5 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,144 - Thread-5 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,410 - Thread-12 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,450 - Thread-72 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,462 - Thread-61 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,548 - Thread-8 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,891 - Thread-5 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:17,936 - Thread-59 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:17,947 - Thread-15 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:18,444 - Thread-77 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:18,610 - Thread-78 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:18,658 - Thread-5 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:18,698 - Thread-66 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:18,894 - Thread-93 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:19,020 - Thread-95 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:19,076 - Thread-84 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:19,300 - Thread-34 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:19,362 - Thread-60 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'web.facebook.com'. (_ssl.c:1006)")': /login.php?_rdc=1&_rdr
2025-05-25 13:19:19,409 - Thread-5 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:19,486 - Thread-12 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:20,126 - Thread-88 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:20,827 - Thread-42 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:21,177 - Thread-7 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:21,327 - Thread-8 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php?_rdc=1&_rdr
2025-05-25 13:19:21,652 - Thread-83 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:22,332 - Thread-58 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:22,548 - Thread-41 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:22,782 - Thread-44 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:22,810 - Thread-98 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:22,994 - Thread-97 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:23,199 - Thread-53 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:23,253 - Thread-43 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:23,277 - Thread-70 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:23,369 - Thread-62 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:23,573 - Thread-48 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:23,869 - Thread-51 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:24,525 - Thread-57 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:24,548 - Thread-47 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:24,758 - Thread-32 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:24,957 - Thread-72 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:24,971 - Thread-61 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:25,084 - Thread-38 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:25,653 - Thread-15 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:25,671 - Thread-37 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:26,036 - Thread-59 (worker) - INFO - Unknown redirect url: https://www.facebook.com/login/help.php?st=opw&lwv=120&lwc=1348060
2025-05-25 13:19:26,524 - Thread-95 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:26,577 - Thread-84 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:26,604 - Thread-28 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:26,613 - Thread-75 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:27,051 - Thread-79 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:27,077 - Thread-56 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:27,507 - Thread-45 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:28,112 - Thread-4 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:28,499 - Thread-28 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:28,748 - Thread-75 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:28,872 - Thread-16 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:28,902 - Thread-23 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:29,064 - Thread-78 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:29,557 - Thread-25 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:29,700 - Thread-93 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:29,833 - Thread-58 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:30,115 - Thread-34 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:30,173 - Thread-60 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php?_rdc=1&_rdr
2025-05-25 13:19:30,284 - Thread-12 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:30,291 - Thread-44 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:30,485 - Thread-28 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:30,584 - Thread-9 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:30,755 - Thread-75 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:30,923 - Thread-88 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:30,948 - Thread-96 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,011 - Thread-101 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:32,033 - Thread-102 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:32,244 - Thread-28 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,456 - Thread-83 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,457 - Thread-72 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,476 - Thread-61 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,492 - Thread-32 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,545 - Thread-76 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:32,551 - Thread-68 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:32,761 - Thread-75 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:33,619 - Thread-98 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:33,803 - Thread-97 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:34,079 - Thread-84 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:34,095 - Thread-70 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:34,193 - Thread-62 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:34,211 - Thread-28 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:34,408 - Thread-48 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:34,450 - Thread-81 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:34,809 - Thread-75 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:35,106 - Thread-35 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:35,462 - Thread-57 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:35,463 - Thread-47 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:35,961 - Thread-7 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:36,231 - Thread-57 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:36,291 - Thread-28 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:36,389 - Thread-52 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:36,471 - Thread-37 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:36,544 - Thread-14 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:36,924 - Thread-75 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:36,985 - Thread-57 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:37,047 - Thread-25 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:37,334 - Thread-58 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:37,666 - Thread-60 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php?_rdc=1&_rdr
2025-05-25 13:19:37,764 - Thread-57 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:37,806 - Thread-44 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:37,858 - Thread-79 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:37,914 - Thread-56 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:38,289 - Thread-59 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:38,510 - Thread-57 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:38,770 - Thread-55 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:39,264 - Thread-57 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:39,487 - Thread-78 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:39,511 - Thread-101 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:39,704 - Thread-16 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:39,976 - Thread-61 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:39,988 - Thread-72 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:40,021 - Thread-57 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:40,211 - Thread-32 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:40,445 - Thread-90 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:40,506 - Thread-93 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:40,697 - Thread-11 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:40,928 - Thread-34 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:41,572 - Thread-84 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:41,952 - Thread-81 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:43,174 - Thread-50 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:43,268 - Thread-83 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:43,305 - Thread-29 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:43,893 - Thread-52 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:43,896 - Thread-31 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:44,233 - Thread-70 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:44,401 - Thread-63 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:44,420 - Thread-98 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:44,541 - Thread-25 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:44,603 - Thread-97 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:44,837 - Thread-58 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:45,299 - Thread-44 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:45,730 - Thread-67 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:45,793 - Thread-59 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:46,155 - Thread-20 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:46,284 - Thread-47 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:46,392 - Thread-7 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:46,662 - Thread-18 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:47,010 - Thread-101 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:47,282 - Thread-37 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:47,500 - Thread-61 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:47,586 - Thread-19 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate (_ssl.c:1006)')': /login.php
2025-05-25 13:19:47,961 - Thread-32 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:48,046 - Thread-85 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:48,318 - Thread-19 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:48,537 - Thread-12 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:48,661 - Thread-79 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:48,727 - Thread-56 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:48,914 - Thread-77 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:49,078 - Thread-84 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:49,454 - Thread-81 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:49,709 - Thread-36 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:49,903 - Thread-78 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:50,526 - Thread-16 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:51,238 - Thread-42 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:51,256 - Thread-69 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:51,270 - Thread-90 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:51,420 - Thread-52 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:51,495 - Thread-11 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:51,746 - Thread-34 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:52,046 - Thread-25 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:52,341 - Thread-58 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:53,195 - Thread-40 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:53,298 - Thread-59 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:53,653 - Thread-53 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:54,086 - Thread-83 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:54,511 - Thread-101 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:55,013 - Thread-61 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:55,025 - Thread-70 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:55,228 - Thread-98 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:55,423 - Thread-97 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:55,479 - Thread-75 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:55,536 - Thread-38 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:55,713 - Thread-32 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:55,725 - Thread-15 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:56,163 - Thread-65 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 737, in _error_catcher
    yield
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1187, in read_chunked
    chunk = self._handle_chunk(amt)
            ^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1129, in _handle_chunk
    value = self._fp._safe_read(amt)  # type: ignore[union-attr]
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\http\client.py", line 631, in _safe_read
    data = self.fp.read(amt)
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\socket.py", line 706, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\ssl.py", line 1311, in recv_into
    return self.read(nbytes, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\ssl.py", line 1167, in read
    return self._sslobj.read(len, buffer)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\models.py", line 820, in generate
    yield from self.raw.stream(chunk_size, decode_content=True)
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1040, in stream
    yield from self.read_chunked(amt, decode_content=decode_content)
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 1172, in read_chunked
    with self._error_catcher():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\response.py", line 742, in _error_catcher
    raise ReadTimeoutError(self._pool, None, "Read timed out.") from e  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 746, in send
    r.content
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\models.py", line 902, in content
    self._content = b"".join(self.iter_content(CONTENT_CHUNK_SIZE)) or b""
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\models.py", line 826, in generate
    raise ConnectionError(e)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out.
2025-05-25 13:19:56,563 - Thread-67 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:56,572 - Thread-84 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:19:56,820 - Thread-7 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:56,971 - Thread-81 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:57,660 - Thread-26 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:58,758 - Thread-69 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:58,913 - Thread-52 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:59,346 - Thread-74 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:19:59,545 - Thread-56 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:59,548 - Thread-25 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:19:59,754 - Thread-79 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:00,335 - Thread-78 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:20:00,480 - Thread-49 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:00,562 - Thread-18 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:00,631 - Thread-50 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:00,795 - Thread-59 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:01,087 - Thread-103 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:01,328 - Thread-16 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:01,508 - Thread-4 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:01,687 - Thread-64 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:01,995 - Thread-73 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:02,017 - Thread-101 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:20:02,096 - Thread-90 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:02,320 - Thread-11 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:02,448 - Thread-51 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:02,562 - Thread-34 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:02,565 - Thread-49 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:02,849 - Thread-21 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:03,149 - Thread-76 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:03,345 - Thread-94 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:03,472 - Thread-32 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:20:03,541 - Thread-91 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,061 - Thread-66 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:04,472 - Thread-81 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,490 - Thread-72 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,616 - Thread-49 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,786 - Thread-9 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,789 - Thread-99 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,880 - Thread-22 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:04,917 - Thread-83 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:05,064 - Thread-6 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:05,464 - Thread-102 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:05,719 - Thread-35 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:06,057 - Thread-19 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:06,074 - Thread-86 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:06,189 - Thread-47 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:06,263 - Thread-69 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:06,294 - Thread-75 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:06,429 - Thread-52 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:06,895 - Thread-96 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:06,926 - Thread-47 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,020 - Thread-14 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:07,054 - Thread-25 (worker) - ERROR - Error in post
urllib3.exceptions.SSLError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 589, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 877, in urlopen
    return self.urlopen(
           ^^^^^^^^^^^^^
  [Previous line repeated 2 more times]
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\connectionpool.py", line 847, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\urllib3\util\retry.py", line 515, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Code\Python\mfb\modules\base_session.py", line 74, in get
    response = super().get(url, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\Code\Python\mfb\.venv\Lib\site-packages\requests\adapters.py", line 620, in send
    raise SSLError(e, request=request)
requests.exceptions.SSLError: HTTPSConnectionPool(host='www.facebook.com', port=443): Max retries exceeded with url: /login.php (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')))
2025-05-25 13:20:07,189 - Thread-49 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,222 - Thread-95 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:07,244 - Thread-79 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,248 - Thread-7 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,375 - Thread-67 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,386 - Thread-84 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,546 - Thread-46 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,676 - Thread-47 (worker) - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:07,732 - Thread-100 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='www.facebook.com', port=443): Read timed out. (read timeout=30)")': /login.php
2025-05-25 13:20:08,202 - Thread-12 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:08,324 - Thread-59 (worker) - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:08,425 - Thread-47 (worker) - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:08,475 - Thread-44 (worker) - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:08,490 - Thread-26 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
2025-05-25 13:20:08,664 - Thread-96 (worker) - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1006)')': /login.php
