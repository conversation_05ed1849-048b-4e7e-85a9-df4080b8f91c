#!/usr/bin/env python3
"""
Performance test example for queue backup system
"""
import time
import json
import os
from modules.task import Task

def create_test_data(size):
    """Create test data with specified size"""
    test_data = []
    for i in range(size):
        test_data.append(f"user{i}:password{i}")
    return test_data

def test_performance():
    """Test performance with different data sizes"""
    
    sizes = [100000, 1000000, 5000000]  # 100K, 1M, 5M
    
    for size in sizes:
        print(f"\n=== Testing with {size:,} items ===")
        
        # Create test data
        test_data = create_test_data(size)
        
        # Test JSON format (manual)
        start_time = time.time()
        json_data = {
            "queue_data": test_data,
            "statistics": {"count": len(test_data)}
        }
        with open(f"test_json_{size}.json", 'w') as f:
            json.dump(json_data, f)
        json_save_time = time.time() - start_time
        
        # Test streaming format (manual)
        start_time = time.time()
        with open(f"test_stream_{size}.txt", 'w') as f:
            for item in test_data:
                f.write(f"{item}\n")
        stream_save_time = time.time() - start_time
        
        # Test loading
        start_time = time.time()
        with open(f"test_json_{size}.json", 'r') as f:
            loaded_json = json.load(f)
        json_load_time = time.time() - start_time
        
        start_time = time.time()
        loaded_stream = []
        with open(f"test_stream_{size}.txt", 'r') as f:
            for line in f:
                loaded_stream.append(line.strip())
        stream_load_time = time.time() - start_time
        
        # Results
        print(f"JSON Save:    {json_save_time:.2f}s")
        print(f"Stream Save:  {stream_save_time:.2f}s")
        print(f"JSON Load:    {json_load_time:.2f}s") 
        print(f"Stream Load:  {stream_load_time:.2f}s")
        print(f"Speedup:      {json_save_time/stream_save_time:.1f}x faster save")
        print(f"Speedup:      {json_load_time/stream_load_time:.1f}x faster load")
        
        # File sizes
        json_size = os.path.getsize(f"test_json_{size}.json")
        stream_size = os.path.getsize(f"test_stream_{size}.txt")
        print(f"JSON Size:    {json_size/1024/1024:.1f}MB")
        print(f"Stream Size:  {stream_size/1024/1024:.1f}MB")
        print(f"Size Ratio:   {json_size/stream_size:.1f}x smaller")
        
        # Cleanup
        os.remove(f"test_json_{size}.json")
        os.remove(f"test_stream_{size}.txt")

if __name__ == "__main__":
    test_performance()
