#!/usr/bin/env python3
"""
Example usage of QueueManager for different scenarios
"""
import time
import logging
from modules.queue_manager import QueueManager

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def demo_small_dataset():
    """Demo with small dataset (uses JSON format)"""
    print("\n=== Demo Small Dataset (JSON Format) ===")
    
    # Create queue manager
    qm = QueueManager("demo_small")
    
    # Add some test data
    test_data = [f"user{i}:password{i}" for i in range(1000)]
    for item in test_data:
        qm.put(item)
    
    print(f"Added {len(test_data)} items to queue")
    print(f"Queue size: {qm.qsize()}")
    
    # Save to backup
    statistics = {"processed": 100, "success": 80}
    success = qm.save_to_backup(statistics)
    print(f"Save backup: {'Success' if success else 'Failed'}")
    
    # Clear queue
    while not qm.empty():
        qm.get_nowait()
    print(f"Queue cleared, size: {qm.qsize()}")
    
    # Load from backup
    success, restored_stats = qm.load_from_backup()
    print(f"Load backup: {'Success' if success else 'Failed'}")
    print(f"Restored queue size: {qm.qsize()}")
    print(f"Restored statistics: {restored_stats}")
    
    # Cleanup
    qm.cleanup_backup()

def demo_large_dataset():
    """Demo with large dataset (uses streaming format)"""
    print("\n=== Demo Large Dataset (Streaming Format) ===")
    
    # Create queue manager
    qm = QueueManager("demo_large")
    
    # Set lower threshold for demo
    qm.set_streaming_threshold(500)  # Use streaming for > 500 items
    
    # Add large test data
    test_data = [f"user{i}:password{i}" for i in range(2000)]
    for item in test_data:
        qm.put(item)
    
    print(f"Added {len(test_data)} items to queue")
    print(f"Queue size: {qm.qsize()}")
    
    # Save to backup (should use streaming format)
    statistics = {"processed": 500, "success": 400}
    start_time = time.time()
    success = qm.save_to_backup(statistics)
    save_time = time.time() - start_time
    print(f"Save backup: {'Success' if success else 'Failed'} ({save_time:.3f}s)")
    
    # Clear queue
    while not qm.empty():
        qm.get_nowait()
    print(f"Queue cleared, size: {qm.qsize()}")
    
    # Load from backup
    start_time = time.time()
    success, restored_stats = qm.load_from_backup()
    load_time = time.time() - start_time
    print(f"Load backup: {'Success' if success else 'Failed'} ({load_time:.3f}s)")
    print(f"Restored queue size: {qm.qsize()}")
    print(f"Restored statistics: {restored_stats}")
    
    # Cleanup
    qm.cleanup_backup()

def demo_file_loading():
    """Demo loading from original file"""
    print("\n=== Demo File Loading ===")
    
    # Create test file
    test_file = "test_data.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        for i in range(100):
            f.write(f"testuser{i}:testpass{i}\n")
    
    # Create queue manager and load from file
    qm = QueueManager("demo_file")
    success = qm.load_from_file(test_file)
    print(f"Load from file: {'Success' if success else 'Failed'}")
    print(f"Queue size after loading: {qm.qsize()}")
    
    # Process some items
    processed = 0
    while not qm.empty() and processed < 50:
        item = qm.get_nowait()
        processed += 1
        qm.task_done()
    
    print(f"Processed {processed} items, remaining: {qm.qsize()}")
    
    # Save remaining items
    statistics = {"processed": processed}
    qm.save_to_backup(statistics)
    print("Saved remaining items to backup")
    
    # Cleanup
    qm.cleanup_backup()
    import os
    os.remove(test_file)

def demo_performance_comparison():
    """Demo performance comparison between formats"""
    print("\n=== Performance Comparison ===")
    
    sizes = [1000, 10000, 100000]
    
    for size in sizes:
        print(f"\nTesting with {size:,} items:")
        
        # Test JSON format
        qm_json = QueueManager(f"perf_json_{size}")
        qm_json.set_streaming_threshold(999999999)  # Force JSON
        
        # Add data
        for i in range(size):
            qm_json.put(f"user{i}:pass{i}")
        
        # Test save/load JSON
        start_time = time.time()
        qm_json.save_to_backup({"test": True})
        json_save_time = time.time() - start_time
        
        # Clear and reload
        while not qm_json.empty():
            qm_json.get_nowait()
        
        start_time = time.time()
        qm_json.load_from_backup()
        json_load_time = time.time() - start_time
        
        # Test streaming format
        qm_stream = QueueManager(f"perf_stream_{size}")
        qm_stream.set_streaming_threshold(1)  # Force streaming
        
        # Add data
        for i in range(size):
            qm_stream.put(f"user{i}:pass{i}")
        
        # Test save/load streaming
        start_time = time.time()
        qm_stream.save_to_backup({"test": True})
        stream_save_time = time.time() - start_time
        
        # Clear and reload
        while not qm_stream.empty():
            qm_stream.get_nowait()
        
        start_time = time.time()
        qm_stream.load_from_backup()
        stream_load_time = time.time() - start_time
        
        # Results
        print(f"  JSON:     Save {json_save_time:.3f}s, Load {json_load_time:.3f}s")
        print(f"  Streaming: Save {stream_save_time:.3f}s, Load {stream_load_time:.3f}s")
        if json_save_time > 0 and stream_save_time > 0:
            print(f"  Speedup:   Save {json_save_time/stream_save_time:.1f}x, Load {json_load_time/stream_load_time:.1f}x")
        
        # Cleanup
        qm_json.cleanup_backup()
        qm_stream.cleanup_backup()

if __name__ == "__main__":
    print("QueueManager Demo")
    print("=" * 50)
    
    demo_small_dataset()
    demo_large_dataset()
    demo_file_loading()
    demo_performance_comparison()
    
    print("\n" + "=" * 50)
    print("Demo completed!")
