import threading
from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os

class APIServer:
    def __init__(self, application):
        self.app = Flask(__name__)
        CORS(self.app)
        self.application = application
        self.server_thread = None
        self.running = False
        self.setup_routes()
        
    def setup_routes(self):
        # Task management endpoints
        @self.app.route('/api/tasks', methods=['GET'])
        def get_tasks():
            # Return list of tasks
            return jsonify({"tasks": self.application.get_tasks()})
            
        @self.app.route('/api/tasks', methods=['POST'])
        def create_task():
            # Create new task
            task_data = request.json
            task_id = self.application.create_task(task_data)
            return jsonify({"task_id": task_id})
            
        @self.app.route('/api/tasks/<task_id>', methods=['DELETE'])
        def delete_task(task_id):
            # Delete task
            success = self.application.delete_task(task_id)
            return jsonify({"success": success})
            
        # Task control endpoints
        @self.app.route('/api/tasks/<task_id>/start', methods=['POST'])
        def start_task(task_id):
            success = self.application.start_task(task_id)
            return jsonify({"success": success})
            
        @self.app.route('/api/tasks/<task_id>/pause', methods=['POST'])
        def pause_task(task_id):
            success = self.application.pause_task(task_id)
            return jsonify({"success": success})
            
        @self.app.route('/api/tasks/<task_id>/resume', methods=['POST'])
        def resume_task(task_id):
            success = self.application.resume_task(task_id)
            return jsonify({"success": success})
            
        # Task status endpoint
        @self.app.route('/api/tasks/<task_id>/status', methods=['GET'])
        def get_task_status(task_id):
            status = self.application.get_task_status(task_id)
            return jsonify(status)
            
        # Server status endpoint
        @self.app.route('/api/status', methods=['GET'])
        def get_server_status():
            return jsonify({
                "server_id": self.application.server_id,
                "version": self.application.version,
                "active_tasks": len(self.application.get_active_tasks())
            })
    
    def start(self, host='0.0.0.0', port=5000):
        if self.running:
            return False
            
        def run_server():
            self.app.run(host=host, port=port, threaded=True)
            
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.running = True
        return True
        
    def stop(self):
        # Flask doesn't have a clean shutdown mechanism in this context
        # In production, you'd use a proper WSGI server like Gunicorn
        self.running = False