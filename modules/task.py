import re
import threading
import time
import queue
import os
import logging
import datetime
import json

class Task:
    def __init__(self, task_id, config, application):
        self.task_id = task_id
        self.config = config
        self.application = application
        self.name = config.get("name", f"Task-{task_id[:8]}")
        self.file_path = config.get("file_path", "")
        self.max_workers = config.get("max_workers", 5)
        self.min_like = config.get("min_like", 0)
        
        # Task state
        self.running = False
        self.paused = False
        self.pause_event = threading.Event()
        self.stop_event = threading.Event()
        
        # Task statistics
        self.data_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.processed_count = 0
        self.processed_lock = threading.Lock()
        self.start_time = 0
        self.two_factor_count = 0
        self.zero_form_count = 0
        self.chat_us_count = 0
        self.access_count = 0
        self.checkpoint_count = 0
        
        # Task threads
        self.threads = []
        self.monitor_thread = None
        self.result_handler_thread = None
    
    def get_info(self):
        return {
            "id": self.task_id,
            "name": self.name,
            "file_path": self.file_path,
            "max_workers": self.max_workers,
            "min_like": self.min_like,
            "running": self.running,
            "paused": self.paused
        }
    
    def get_status(self):
        elapsed = time.time() - self.start_time if self.start_time > 0 else 0
        cpm = self.processed_count * 60 if elapsed > 0 else 0
        
        return {
            "id": self.task_id,
            "name": self.name,
            "running": self.running,
            "paused": self.paused,
            "queue_size": self.data_queue.qsize() if self.running else 0,
            "thread_count": len([t for t in self.threads if t.is_alive()]),
            "two_factor_count": self.two_factor_count,
            "zero_form_count": self.zero_form_count,
            "chat_us_count": self.chat_us_count,
            "access_count": self.access_count,
            "checkpoint_count": self.checkpoint_count,
            "cpm": int(cpm),
            "elapsed_time": elapsed
        }
    
    def start(self):
        if self.running:
            return False
        
        # Reset events
        self.stop_event.clear()
        self.pause_event.set()  # Not paused initially
        
        # Load data
        if not self._load_data():
            return False
        
        # Start threads
        self.running = True
        self.paused = False
        self.start_time = time.time()
        
        # Start monitor thread
        self.monitor_thread = threading.Thread(target=self._monitor_stats, daemon=True)
        self.monitor_thread.start()
        
        # Start result handler thread
        self.result_handler_thread = threading.Thread(target=self._handle_result, daemon=True)
        self.result_handler_thread.start()
        
        # Start worker threads
        max_workers = min(self.max_workers, self.data_queue.qsize())
        for i in range(max_workers):
            worker_thread = threading.Thread(target=self._worker, daemon=True)
            worker_thread.start()
            self.threads.append(worker_thread)
        
        return True
    
    def pause(self):
        if not self.running or self.paused:
            return False
        
        self.pause_event.clear()  # Signal threads to pause
        self.paused = True
        return True
    
    def resume(self):
        if not self.running or not self.paused:
            return False
        
        self.pause_event.set()  # Signal threads to resume
        self.paused = False
        return True
    
    def stop(self):
        if not self.running:
            return False
        
        self.stop_event.set()  # Signal threads to stop
        self.pause_event.set()  # Ensure threads aren't paused
        
        # Put None in queues to signal threads to exit
        self.data_queue.put(None)
        self.result_queue.put(None)
        
        # Wait for threads to finish
        for thread in self.threads:
            thread.join(timeout=1.0)
        
        if self.result_handler_thread:
            self.result_handler_thread.join(timeout=1.0)
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        
        self.running = False
        self.paused = False
        return True
    
    def _load_data(self):
        if not self.file_path or not os.path.exists(self.file_path):
            logging.error(f"File not found: {self.file_path}")
            return False
        
        self.data_queue = queue.Queue()
        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line or len(line) > 65 or (":" not in line and "|" not in line):
                    continue
                self.data_queue.put(line)
        
        if self.data_queue.empty():
            logging.error(f"No valid data found in file: {self.file_path}")
            return False
            
        return True
    
    # Worker methods similar to Application class but with pause/stop support
    def _worker(self):
        while not self.stop_event.is_set():
            # Check if paused
            self.pause_event.wait()
            if self.stop_event.is_set():
                break
                
            try:
                # Get data with timeout to check for pause/stop regularly
                try:
                    data = self.data_queue.get(timeout=0.5)
                except queue.Empty:
                    continue
                    
                if data is None:
                    self.data_queue.put(None)  # Propagate to other workers
                    break
                    
                # Process data (similar to Application.process_data)
                strs = re.split(r"[|:]", data)
                if len(strs) < 2:
                    continue
                username = strs[0].strip()
                password = strs[1].strip()
                
                # Process the data (would call process_data equivalent)
                # ...
                
                with self.processed_lock:
                    self.processed_count += 1
                    
            except Exception as ex:
                logging.exception(f"Error in worker thread for task {self.task_id}")
            finally:
                self.data_queue.task_done()
    
    def _handle_result(self):
        # Similar to Application.handle_result but with pause/stop support
        pass
        
    def _monitor_stats(self):
        # Similar to Application.monitor_stats but with pause/stop support
        pass