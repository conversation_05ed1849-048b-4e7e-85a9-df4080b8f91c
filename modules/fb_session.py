import logging
import string

from modules import base_session
from modules.base_session import *


class FBSession(base_session.BaseSession):

    def __init__(self, proxy_config=None, username=None, password=None):
        super().__init__(proxy_config)
        self.username = username
        self.password = password
        self.headers.update(rq_web_header())


    def login(self, retry=0):
        try:
            login_data = self.load_login_config()
            if not login_data:
                raise Exception("Failed to load login config")
            timezone = str(random.choice([i * 15 for i in range(-48, 57)]))
            text_rd = ''.join(random.choices(string.ascii_letters, k=4))
            lgnrnd = str(random.randint(100000, 999999)) + '_' + text_rd
            lgnjs = str(int(datetime.datetime.now().timestamp()) + int(timezone))
            key_id = login_data.get("key_id", "")
            public_key = login_data.get("public_key", "")
            password_enc = generate_enc_pass(key_id, public_key, self.password)
            data = {
                'jazoest': login_data.get("jazoest", ""),
                'lsd': login_data.get("lsd", ""),
                'display': '',
                'isprivate': '',
                'return_session': '',
                'skip_api_login': '',
                'signed_next': '',
                'trynum': str(retry),
                'timezone': timezone,
                'lgndim': login_data.get("lgndim", ""),
                'lgnrnd': lgnrnd,
                'lgnjs': lgnjs,
                'email': self.username,
                'prefill_contact_point': self.username,
                'prefill_source': 'browser_dropdown',
                'prefill_type': 'password',
                'first_prefill_source': 'browser_dropdown',
                'first_prefill_type': 'contact_point',
                'had_cp_prefilled': 'true',
                'had_password_prefilled': 'true',
                'ab_test_data': '',
                'encpass': password_enc,
            }
            url = 'https://www.facebook.com/login/device-based/regular/login/?login_attempt=1&lwv=100'
            self.update_cookie_with_window_size()
            response = self.post(url, data=data, allow_redirects=False)
            if response.status_code == 302:
                redirect_url = response.headers.get("Location")
                uid = self.get_uid_from_cookie()
                if uid:
                    if redirect_url.startswith('https://www.facebook.com/?'):
                        return {
                            "status": True,
                            "type": "Access",
                            "user_id": uid,
                            "cookie": self.get_cookie_string()
                        }
                    elif 'flow=two_factor_login' in redirect_url or 'checkpoint/?next' in redirect_url:
                        return {
                            "status": True,
                            "type": "2FA",
                            "user_id": uid
                        }
                    elif '2217681/?next' in redirect_url:
                        return {
                            "status": True,
                            "type": "681",
                            "user_id": uid
                        }
                elif 'checkpoint/disabled' in redirect_url:
                    return {
                        "status": False,
                        "type": "disabled"
                    }
                elif 'flow=pre_authentication' in redirect_url:
                    if retry < 3:
                        self.set_proxy(True)
                        return self.login(retry + 1)
                    return {
                        "status": False,
                        "type": "Captcha"
                    }
                elif ('login_attempt' in redirect_url or
                      'login/help.php' in redirect_url or
                      'recover/initiate' in redirect_url):
                    return {
                        "status": False,
                        "type": "Wrong Password"
                    }
                else:
                    logging.info(f"Unknown redirect url: {redirect_url}")
                    return {
                        "status": False,
                        "type": "Unknown"
                    }
            # if retry < 3:
            #     return self.login(3)
            return {
                    "status": False,
                    "type": "Wrong Password"
                }
        except Exception as e:
            logging.exception("Error in login")
            if retry < 3:
                return self.login(retry + 1)
            else:
                raise e

    def check_chat_us(self):
        try:
            self.headers.update(web_to_mobile_header())
            response = self.get("https://m.facebook.com/checkpoint/?next&having_trouble=1")
            html = response.text
            is_chat_us = "MCheckpointLiveChatClickEvent" in html or "facebook.com/support/chat/?token=" in html
            is_uploadable = 'no_phone_access' in html
            return is_chat_us, is_uploadable
        except Exception as e:
            print(e)
            return False, False

    def is_live(self):
        try:
            response = self.get("https://m.facebook.com/checkpoint/?next&having_trouble=1")
            html = response.text
            is_live = "MCheckpointLiveChatClickEvent" in html or "facebook.com/support/chat/?token=" in html
            return is_live
        except Exception as e:
            print(e)
            return False

    # def check_result_type(self):
    #     try:
    #         response = self.get("https://www.facebook.com/checkpoint/?next&no_fido=true&locale=en_GB")
    #         html = response.text
    #         is_chat_live = False
    #         is_upload_image = False
    #         if "MCheckpointLiveChatClickEvent" in html or "facebook.com/support/chat/?token=" in html:
    #             is_chat_live = True
    #         if "AuthenticityIDUpload" in html:
    #             is_upload_image = True
    #         return is_chat_live, is_upload_image
    #     except Exception as e:
    #         print(e)
    #         return False, False

    def load_login_config(self, retry=0):
        # Load the login configuration here
        try:
            response = self.get("https://www.facebook.com/login.php")
            html = response.text
            info = get_encrypt_password(html)
            if info.get("key_id", 0) == 0 or info.get("public_key", "") == "":
                raise Exception("Failed to get key_id or public_key")
            info["jazoest"] = get_jazoest(html)
            info["lsd"] = get_lsd(html)
            lgndim_matcher = re.search(r"privacy_mutation_token=(.*?)&", html)
            if lgndim_matcher:
                info["lgndim"] = lgndim_matcher.group(1)
            else:
                info["lgndim"] = ""
            return info
        except Exception as e:
            if retry < 3:
                return self.load_login_config(retry + 1)
            else:
                raise e


