import os
import json
import logging
import datetime
import queue
from typing import List, Dict, Any, Optional


class QueueManager:
    """
    Quản lý queue với khả năng lưu trữ và khôi phục dữ liệu
    Hỗ trợ cả JSON format (dữ liệu nhỏ) và streaming format (dữ liệu lớn)
    """
    
    def __init__(self, task_id: str, backup_dir: str = "Results/queue_backup"):
        self.task_id = task_id
        self.backup_dir = backup_dir
        self.data_queue = queue.Queue()
        
        # Tạo thư mục backup nếu chưa tồn tại
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Đường dẫn các file backup
        self.json_backup_file = os.path.join(self.backup_dir, f"task_{self.task_id}_queue.json")
        self.metadata_file = os.path.join(self.backup_dir, f"task_{self.task_id}_meta.json")
        self.data_file = os.path.join(self.backup_dir, f"task_{self.task_id}_data.txt")
        
        # Ngưỡng chuyển đổi format (có thể cấu hình)
        self.streaming_threshold = 1000000  # 1M items
        
    def put(self, item: Any) -> None:
        """Thêm item vào queue"""
        self.data_queue.put(item)
    
    def get(self, timeout: Optional[float] = None) -> Any:
        """Lấy item từ queue"""
        if timeout is None:
            return self.data_queue.get()
        else:
            return self.data_queue.get(timeout=timeout)
    
    def get_nowait(self) -> Any:
        """Lấy item từ queue không chờ"""
        return self.data_queue.get_nowait()
    
    def task_done(self) -> None:
        """Đánh dấu task hoàn thành"""
        self.data_queue.task_done()
    
    def qsize(self) -> int:
        """Trả về kích thước queue"""
        return self.data_queue.qsize()
    
    def empty(self) -> bool:
        """Kiểm tra queue có rỗng không"""
        return self.data_queue.empty()
    
    def load_from_file(self, file_path: str) -> bool:
        """
        Load dữ liệu từ file gốc vào queue
        
        Args:
            file_path: Đường dẫn file dữ liệu gốc
            
        Returns:
            bool: True nếu load thành công
        """
        try:
            if not os.path.exists(file_path):
                logging.error(f"File not found: {file_path}")
                return False
            
            self.data_queue = queue.Queue()
            loaded_count = 0
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                for line in file:
                    line = line.strip()
                    if not line or len(line) > 65 or (":" not in line and "|" not in line):
                        continue
                    self.data_queue.put(line)
                    loaded_count += 1
            
            if loaded_count == 0:
                logging.error(f"No valid data found in file: {file_path}")
                return False
            
            logging.info(f"Loaded {loaded_count} items from file: {file_path}")
            return True
            
        except Exception as ex:
            logging.exception(f"Failed to load data from file {file_path}: {ex}")
            return False
    
    def save_to_backup(self, statistics: Dict[str, Any] = None) -> bool:
        """
        Lưu queue hiện tại vào file backup
        
        Args:
            statistics: Thống kê task để lưu cùng
            
        Returns:
            bool: True nếu lưu thành công
        """
        try:
            # Trích xuất tất cả items từ queue
            queue_data = []
            while not self.data_queue.empty():
                try:
                    item = self.data_queue.get_nowait()
                    if item is not None:  # Bỏ qua None terminator
                        queue_data.append(item)
                except queue.Empty:
                    break
            
            if not queue_data:
                logging.info(f"No data to save for task {self.task_id}")
                return True
            
            # Chọn format dựa trên kích thước dữ liệu
            data_count = len(queue_data)
            
            if data_count > self.streaming_threshold:
                success = self._save_streaming_format(queue_data, statistics)
                format_name = "streaming"
            else:
                success = self._save_json_format(queue_data, statistics)
                format_name = "JSON"
            
            if success:
                logging.info(f"Saved {data_count} items using {format_name} format for task {self.task_id}")
            
            return success
            
        except Exception as ex:
            logging.exception(f"Failed to save queue backup for task {self.task_id}: {ex}")
            return False
    
    def load_from_backup(self) -> tuple[bool, Optional[Dict[str, Any]]]:
        """
        Load dữ liệu từ file backup
        
        Returns:
            tuple: (success, statistics) - success là bool, statistics là dict hoặc None
        """
        try:
            # Kiểm tra format nào tồn tại
            if os.path.exists(self.metadata_file) and os.path.exists(self.data_file):
                return self._load_streaming_format()
            elif os.path.exists(self.json_backup_file):
                return self._load_json_format()
            else:
                logging.warning(f"No backup files found for task {self.task_id}")
                return False, None
                
        except Exception as ex:
            logging.exception(f"Failed to load queue backup for task {self.task_id}: {ex}")
            return False, None
    
    def has_backup(self) -> bool:
        """Kiểm tra có file backup không"""
        return (os.path.exists(self.json_backup_file) or 
                (os.path.exists(self.metadata_file) and os.path.exists(self.data_file)))
    
    def cleanup_backup(self) -> None:
        """Xóa các file backup"""
        try:
            files_to_remove = [self.json_backup_file, self.metadata_file, self.data_file]
            for file_path in files_to_remove:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logging.debug(f"Removed backup file: {file_path}")
        except Exception as ex:
            logging.exception(f"Failed to cleanup backup files for task {self.task_id}: {ex}")
    
    def _save_json_format(self, queue_data: List[str], statistics: Dict[str, Any] = None) -> bool:
        """Lưu dữ liệu dưới dạng JSON (cho dữ liệu nhỏ)"""
        try:
            backup_data = {
                "task_id": self.task_id,
                "timestamp": datetime.datetime.now().isoformat(),
                "format": "json",
                "data_count": len(queue_data),
                "queue_data": queue_data,
                "statistics": statistics or {}
            }
            
            with open(self.json_backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as ex:
            logging.exception(f"Failed to save JSON format backup for task {self.task_id}: {ex}")
            return False
    
    def _save_streaming_format(self, queue_data: List[str], statistics: Dict[str, Any] = None) -> bool:
        """Lưu dữ liệu dưới dạng streaming (cho dữ liệu lớn)"""
        try:
            # Lưu metadata
            metadata = {
                "task_id": self.task_id,
                "timestamp": datetime.datetime.now().isoformat(),
                "format": "streaming",
                "data_count": len(queue_data),
                "statistics": statistics or {}
            }
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # Lưu dữ liệu dưới dạng text thuần
            with open(self.data_file, 'w', encoding='utf-8') as f:
                for item in queue_data:
                    f.write(f"{item}\n")
            
            return True
            
        except Exception as ex:
            logging.exception(f"Failed to save streaming format backup for task {self.task_id}: {ex}")
            return False
    
    def _load_json_format(self) -> tuple[bool, Optional[Dict[str, Any]]]:
        """Load dữ liệu từ JSON format"""
        try:
            with open(self.json_backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # Khôi phục queue data
            queue_data = backup_data.get("queue_data", [])
            self.data_queue = queue.Queue()
            for item in queue_data:
                self.data_queue.put(item)
            
            # Lấy statistics
            statistics = backup_data.get("statistics", {})
            
            logging.info(f"Loaded {len(queue_data)} items from JSON backup for task {self.task_id}")
            
            # Xóa file backup sau khi load thành công
            os.remove(self.json_backup_file)
            
            return True, statistics
            
        except Exception as ex:
            logging.exception(f"Failed to load JSON format backup for task {self.task_id}: {ex}")
            return False, None
    
    def _load_streaming_format(self) -> tuple[bool, Optional[Dict[str, Any]]]:
        """Load dữ liệu từ streaming format"""
        try:
            # Load metadata
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # Load dữ liệu streaming
            self.data_queue = queue.Queue()
            data_count = 0
            
            with open(self.data_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:  # Bỏ qua dòng trống
                        self.data_queue.put(line)
                        data_count += 1
            
            # Lấy statistics
            statistics = metadata.get("statistics", {})
            
            logging.info(f"Loaded {data_count} items from streaming backup for task {self.task_id}")
            
            # Xóa file backup sau khi load thành công
            os.remove(self.metadata_file)
            os.remove(self.data_file)
            
            return True, statistics
            
        except Exception as ex:
            logging.exception(f"Failed to load streaming format backup for task {self.task_id}: {ex}")
            return False, None
    
    def set_streaming_threshold(self, threshold: int) -> None:
        """Cài đặt ngưỡng chuyển đổi sang streaming format"""
        self.streaming_threshold = threshold
        logging.info(f"Set streaming threshold to {threshold} items for task {self.task_id}")
