import os
import random
import string
from typing import Dict


def parse_proxy(proxy_config: Dict, need_change: bool = False) -> str:
    if not proxy_config:
        raise Exception("PROXY EMPTY")
    username = proxy_config.get('username', '')
    password = proxy_config.get("password", "")
    host = proxy_config.get("host", "")
    port = proxy_config.get("port", "")
    p_type = proxy_config.get("type", "911")
    if need_change:
        strs = string.ascii_letters + string.digits
        s = get_random_str(random.randint(6, 11), strs)
        if p_type == "911":
            name = f"{username}_session-{s}_life-5"
        else:
            name = f"{username}-session-{s}-sessTime-5"
    else:
        name = username
    return f"http://{name}:{password}@{host}:{port}"


def get_random_str(length: int = 23, input_random=string.digits) -> str:
    return ''.join(random.choices(input_random, k=length))


def resolve_relative_path(path: str) -> str:
    return os.path.abspath(os.path.join(os.path.dirname(__file__), path))


def convert_seconds(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}h {minutes:02d}m {seconds:02d}s"
