import base64
import binascii
import datetime
import json
import logging
import random
import re
import struct
from threading import Thread
from typing import Dict
from urllib.parse import unquote

import requests
from requests.adapters import HTTPAdapter
from requests.exceptions import ProxyError, RequestException
from urllib3 import Retry
from urllib3.exceptions import HeaderParsingError

from modules.ua_helper import get_user_agent, get_mobile_user_agent
from modules.utils import parse_proxy

from Cryptodome.Cipher import AES
from Cryptodome import Random as RDM
from nacl.public import PublicKey as PK
from nacl.public import SealedBox as SB


class BaseSession(requests.Session):

    def __init__(self, proxy_config: Dict[str, any] =None):
        super().__init__()
        # if not proxy_config:
        #     raise Exception("PROXY EMPTY")
        self.proxy_config = proxy_config
        self.set_proxy(True)
        self.max_retries = 5

        logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)

        retry_strategy = Retry(
            total=self.max_retries,  # Number of retries
            status_forcelist=[429, 500, 502, 503, 504],
            backoff_factor=0.5,
            # Tắt log retry
            raise_on_redirect=False,
            raise_on_status=False
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.adapters.update({'http://': adapter, 'https://': adapter})
        self.timeout = 30

    def update_cookie_with_window_size(self):
        cookies = randomize_cookies()
        for key, value in cookies.items():
            self.cookies.set(key, value, domain=".facebook.com")

    def count_pages(self, retry=0):
        try:
            response = self.get("https://www.facebook.com/help/contact/302596017821552?locale=en_GB")
            html = response.text
            matches = re.findall(r'"__html":".*?div.*?>.*?\((.*?)\).*?div.*?div>([0-9,]+)', html)
            if matches:
                keys = [match[0].replace("\u003C\/", '').strip() for match in matches]
                values = [match[1] for match in matches]
                pages = dict(zip(keys, values))
                pages = {k: v.replace(",", "") for k, v in pages.items()}
                return pages
            else:
                matches = re.findall(r'"__html":".*?div.*?>.*?\((.*?)\).*?div.*?div>', html)
                return {match: None for match in matches}
        except Exception as e:
            if retry < 3:
                return self.count_pages(retry + 1)
            return {}

    def get(self, url, retry=0, **kwargs):
        try:
            if 'timeout' not in kwargs:
                kwargs['timeout'] = self.timeout
            response = super().get(url, **kwargs)
            if re.search(r'Temporarily Blocked', response.text, re.IGNORECASE):
                self.set_proxy(change=True)
                return self.get(url, retry, **kwargs)
            return response
        except (RequestException, HeaderParsingError, ConnectionError, ProxyError, TimeoutError) as err:
            if retry < self.max_retries:
                self.set_proxy(change=True)
                return self.get(url, retry + 1, **kwargs)
            else:
                raise err

    def post(self, url, data=None, json=None, retry=0, **kwargs):
        try:
            if 'timeout' not in kwargs:
                kwargs['timeout'] = self.timeout
            response = super().post(url, data=data, json=json, **kwargs)
            if re.search(r'Temporarily Blocked', response.text, re.IGNORECASE):
                self.set_proxy(change=True)
                return self.post(url, data=data, json=json, retry=retry, **kwargs)
            return response
        except (RequestException, HeaderParsingError, ConnectionError, ProxyError, TimeoutError) as err:
            if retry < self.max_retries:
                self.set_proxy(change=True)
                return self.post(url, data, json, retry + 1, **kwargs)
            else:
                raise err

    def set_proxy(self, change: bool = False):
        if not self.proxy_config:
            return
        proxy = parse_proxy(self.proxy_config, change)
        self.proxies = {
            'http': proxy,
            'https': proxy
        }

    def get_uid_from_cookie(self):
        try:
            uid = self.cookies.get("c_user", "")
            if uid:
                return uid
            checkpoint_str = unquote(self.cookies.get("checkpoint", ""))
            user_id_matcher = re.search(r'u":(\d+)', checkpoint_str)
            if user_id_matcher:
                return user_id_matcher.group(1)
            return ""
        except:
            return ""

def check_live_uid(uid: str):
    url = f"https://graph.facebook.com/{uid}/picture?redirect=0"
    try:
        response = requests.get(url, timeout=30)
        if response.status_code != 200:
            return True
        if "static.xx" in response.text:
            return False
        return True
    except Exception as e:
        print(e)
        return True


def extract_platform_from_ua(user_agent):
    """Trích xuất thông tin nền tảng từ User-Agent"""
    if "iPhone" in user_agent or "iPad" in user_agent or "iOS" in user_agent:
        return '"iOS"'
    elif "Android" in user_agent:
        return '"Android"'
    elif "Windows" in user_agent:
        return '"Windows"'
    elif "Macintosh" in user_agent or "Mac OS" in user_agent:
        return '"macOS"'
    elif "Linux" in user_agent:
        return '"Linux"'
    else:
        return '"Windows"'  # Mặc định là Windows nếu không xác định được


def rq_mobile_header() -> dict:
    user_agent = get_mobile_user_agent()
    platform = extract_platform_from_ua(user_agent)
    return {
        'user-agent': user_agent,
        "content-type": "application/x-www-form-urlencoded",
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-encoding": "gzip, deflate, br",
        "sec-ch-ua-platform": platform,
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1'
    }

def rq_web_header() -> dict:
    user_agent = get_user_agent()
    platform = extract_platform_from_ua(user_agent)
    return {
        'User-Agent': user_agent,
        "content-type": "application/x-www-form-urlencoded",
        "accept": "*/*",
        'origin': 'https://www.facebook.com',
        "content-encoding": "gzip, deflate, br",
        'sec-ch-prefers-color-scheme': 'light',
        'sec-ch-ua-platform': platform,
        'sec-ch-ua-mobile': '?0',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1'
    }

def web_to_mobile_header():
    user_agent = get_mobile_user_agent()
    platform = extract_platform_from_ua(user_agent)
    return {
            'User-Agent': user_agent,
            'sec-ch-ua-platform': platform,
            'sec-ch-ua-mobile': '?1',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1'
        }

def mobile_to_web_header():
    user_agent = get_user_agent()
    platform = extract_platform_from_ua(user_agent)
    return {
            'User-Agent': user_agent,
            'sec-ch-ua-platform': platform,
            "content-type": None,
            "x-fb-sim-hni": None,
            "x-fb-connection-type": None,
            "x-fb-net-hni": None,
            "x-fb-connection-bandwidth": None,
            "x-fb-connection-quality": None,
            "x-fb-friendly-name": None,
            "accept-encoding": "gzip, deflate",
            "X-FB-HTTP-Engine": None,
            "priority": "u=1, i",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "none",
            "sec-fetch-storage-access": "active"
        }

def generate_enc_pass(key_id: int, public_key: str ,password: str):
    rdb = RDM.get_random_bytes(32)
    wkt = int(datetime.datetime.now().timestamp())
    dpt = AES.new(rdb, AES.MODE_GCM, nonce=bytes([0] * 12), mac_len=16)
    dpt.update(str(wkt).encode("utf-8"))
    epw, ctg = dpt.encrypt_and_digest(password.encode("utf-8"))
    sld = SB(PK(binascii.unhexlify(public_key))).encrypt(rdb)
    ecp = base64.b64encode(
        bytes(
            [
                1,
                key_id,
                *list(struct.pack("<h", len(sld))),
                *list(sld),
                *list(ctg),
                *list(epw),
            ]
        )
    ).decode("utf-8")
    return "#PWD_BROWSER:5:%s:%s" % (wkt, ecp)

def get_encrypt_password(html):
    pattern = re.compile(r'\(bk\.action\.caa\.mobileweb\.EncryptPassword,\s*((?:\([^()]*\)|[^,()])+),\s*(\d+),\s*\\"([^"]+)\\"', re.DOTALL)
    matches = pattern.findall(html)
    public_key = ""
    key_id = 0
    if len(matches) > 0 and len(matches[0]) > 2:
        public_key = matches[0][2]
        key_id = int(matches[0][1])
    else:
        public_key_match = re.search(r'publicKey:"(.*?)"', html)
        if not public_key_match:
            public_key_match = re.search(r'publicKey":"(.*?)"', html)
        if public_key_match:
            public_key = public_key_match.group(1)
        key_id_match = re.search(r'keyId:(\d+)', html)
        if not key_id_match:
            key_id_match = re.search(r'keyId":(\d+)', html)
        if key_id_match:
            key_id = key_id_match.group(1)

    return {
        "key_id": int(key_id),
        "public_key": public_key
    }


def get_jazoest(html):
    ja_matcher = re.search(r'"jazoest" value="(.*?)"', html)
    if not ja_matcher:
        ja_matcher = re.search(r'"jazoest",\s*"(\d+)"', html)
    if ja_matcher:
        return ja_matcher.group(1)
    return None


def get_lsd(html):
    lsd_matcher = re.search(r'"lsd" value="(.*?)"', html)
    if not  lsd_matcher:
        lsd_matcher = re.search(r'LSD",\[],{"token":"(.*?)"', html)
    if not  lsd_matcher:
        lsd_matcher = re.search(r'"lsd",\s*"([^"]+)"', html)
    if lsd_matcher:
        return lsd_matcher.group(1)
    return None


def get_fb_dtsg(html):
    rq_config_marcher = re.search(r'"MRequestConfig",\[],{(.*?)}}', html)
    if rq_config_marcher:
        json_text = "{" + rq_config_marcher.group(1) + "}}"
        rq_config = json.loads(json_text)
        fb_dtsg = rq_config.get("dtsg", {}).get("token", "")
        lsd = rq_config.get("lsd", "")
        return fb_dtsg, lsd
    return None, None

def get_li(html):
    li_matcher = re.search(r'name="li" value="(.*?)"', html)
    if not li_matcher:
        li_matcher = re.search(r'"li",\s*"([^"]+)"', html)
    if li_matcher:
        return li_matcher.group(1)
    return None

def get_a(html):
    a_matcher = re.search(r'encrypted":"(.*?)"', html)
    if a_matcher:
        return a_matcher.group(1)
    return ""

def get_hsi(html):
    hsi_matcher = re.search(r'"hsi":"(.*?)"', html)
    if hsi_matcher:
        return hsi_matcher.group(1)
    return None

def get_rev(html):
    rev_matcher = re.search(r'"server_revision":(\d+)', html)
    if not rev_matcher:
        rev_matcher = re.search(r'"client_revision":(\d+)', html)
    if not rev_matcher:
        rev_matcher = re.search(r'rev:(\d+)', html)
    if rev_matcher:
        return rev_matcher.group(1)
    return None

def get_haste_session(html):
    haste_session_matcher = re.search(r'"haste_session":"(.*?)"', html)
    if haste_session_matcher:
        return haste_session_matcher.group(1)
    return None


def get_input_params(html):
    pattern = re.compile(r'''\(bk\.action\.map\.Make,\s*\(bk\.action\.array\.Make,\s*(.*?)\),\s*\(bk\.action\.array\.Make,\s*(.*?)\)''', re.VERBOSE)
    matches = pattern.findall(html)
    result = {}
    waterfall_id = None
    for matcher in matches:
        title_str = matcher[0].strip().replace("\\", "")
        title_str = title_str.replace("\"", "")
        value_str = matcher[1].strip().replace("\\", "")
        value_str = value_str.replace("\"", "")
        titles = title_str.split(",")
        titles = [tile.strip() for tile in titles]
        values = value_str.split(",")
        values = [value.strip() for value in values if "(bk.action" not in value]
        if "username_text_input_id" in titles and len(values) > 2:
            result["username_text_input_id"] = values[1]
            result["password_text_input_id"] = values[2]
            result["INTERNAL__latency_qpl_marker_id"] = values[len(values) - 2]
            result["INTERNAL__latency_qpl_instance_id"] = values[len(values) - 1]
        if not waterfall_id and "waterfall_id" in titles and len(values) > 0 and values[0] != "":
            waterfall_id = values[0]
    if waterfall_id:
        result["waterfall_id"] = waterfall_id
    return result

def randomize_cookies():
    # Randomize m_pixel_ratio between 1.0 and 3.0 (step 0.01)
    m_pixel_ratio = round(random.uniform(1.0, 3.0), 2)
    # Randomize wd as WIDTHxHEIGHT, e.g., 1024x768 to 2560x1440
    width = random.choice([1024, 1280, 1366, 1440, 1536, 1600, 1920, 2048, 2560])
    height = random.choice([720, 768, 900, 1080, 1152, 1200, 1440])
    wd = f"{width}x{height}"

    cookies = {
        "m_pixel_ratio": str(m_pixel_ratio),
        "wd": wd
    }
    return cookies

def create_login_params(config, username: str, password: str):
    return {
        "server_params": {
            "credential_type": "password",
            "username_text_input_id": config.get("username_text_input_id", "589kwu:67"),
            "password_text_input_id": config.get("password_text_input_id", "589kwu:68"),
            "login_source": "Login",
            "login_credential_type": "none",
            "server_login_source": "login",
            "ar_event_source": "login_home_page",
            "should_trigger_override_login_success_action": 0,
            "should_trigger_override_login_2fa_action": 0,
            "is_caa_perf_enabled": 0,
            "reg_flow_source": "login_home_native_integration_point",
            "caller": "gslr",
            "is_from_landing_page": 0,
            "is_from_empty_password": 0,
            "is_from_aymh": 0,
            "is_from_password_entry_page": 0,
            "is_from_assistive_id": 0,
            "is_from_msplit_fallback": 0,
            "two_step_login_type": "one_step_login",
            "INTERNAL__latency_qpl_marker_id": config.get("INTERNAL__latency_qpl_marker_id", 36707139),
            "INTERNAL__latency_qpl_instance_id": config.get("INTERNAL__latency_qpl_instance_id", "31621481400407"),
            "device_id": None,
            "family_device_id": None,
            "waterfall_id": config.get("waterfall_id", "ae71dfc6-73e3-4707-8e6f-ba7b11244843"),
            "offline_experiment_group": None,
            "layered_homepage_experiment_group": None,
            "is_platform_login": 0,
            "is_from_logged_in_switcher": 0,
            "is_from_logged_out": 0,
            "access_flow_version": "pre_mt_behavior"
        },
        "client_input_params": {
            "machine_id": "",
            "contact_point": username,
            "password": generate_enc_pass(config.get("key_id", ""), config.get("public_key", ""), password),
            "accounts_list": [],
            "fb_ig_device_id": [],
            "secure_family_device_id": "",
            "encrypted_msisdn": "",
            "headers_infra_flow_id": "",
            "try_num": 1,
            "login_attempt_count": 1,
            "event_flow": "login_manual",
            "event_step": "home_page",
            "openid_tokens": {},
            "block_store_machine_id": "",
            "auth_secure_device_id": "",
            "client_known_key_hash": "",
            "has_whatsapp_installed": 0,
            "sso_token_map_json_string": "",
            "should_show_nested_nta_from_aymh": 0,
            "password_contains_non_ascii": "false",
            "has_granted_read_contacts_permissions": 0,
            "has_granted_read_phone_permissions": 0,
            "app_manager_id": "",
            "aymh_accounts": [
                {
                    "id": "",
                    "profiles": {
                        "id": {
                            "user_id": "",
                            "name": "",
                            "profile_picture_url": "",
                            "small_profile_picture_url": None,
                            "notification_count": 0,
                            "credential_type": "none",
                            "token": "",
                            "last_access_time": 0,
                            "is_derived": 0,
                            "username": "",
                            "password": "",
                            "has_smartlock": 0,
                            "account_center_id": "",
                            "account_source": "",
                            "credentials": [],
                            "nta_eligibility_reason": None,
                            "from_accurate_privacy_result": 0,
                            "dbln_validated": 0
                        }
                    }
                }
            ],
            "lois_settings": {
                "lois_token": ""
            }
        }
    }

