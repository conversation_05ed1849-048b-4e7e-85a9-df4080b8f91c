import os
import datetime
import logging
import logging.handlers


class TimestampRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """Handler tùy chỉnh để đặt tên file backup với timestamp"""
    
    def doRollover(self):
        """<PERSON><PERSON> đè phương thức doRollover để đặt tên file backup với timestamp"""
        if self.stream:
            self.stream.close()
            self.stream = None
        
        # Tạo tên file backup với timestamp
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        base_filename = os.path.splitext(self.baseFilename)[0]
        ext = os.path.splitext(self.baseFilename)[1]
        backup_filename = f"{base_filename}_{timestamp}{ext}"
        
        # Đổi tên file hiện tại thành file backup
        if os.path.exists(self.baseFilename):
            os.rename(self.baseFilename, backup_filename)
            
        # Tạo file mới
        self.mode = 'w'
        self.stream = self._open()
        
        # Qu<PERSON>n lý số lượng file backup
        dirName, baseName = os.path.split(self.baseFilename)
        base_without_ext = os.path.splitext(baseName)[0]
        backup_files = []
        
        for fileName in os.listdir(dirName):
            if fileName.startswith(base_without_ext) and fileName != baseName:
                backup_files.append(os.path.join(dirName, fileName))
                
        # Sắp xếp theo thời gian sửa đổi (cũ nhất trước)
        backup_files.sort(key=lambda x: os.path.getmtime(x))
        
        # Xóa các file cũ nếu vượt quá số lượng cho phép
        if len(backup_files) > self.backupCount:
            for old_file in backup_files[:-self.backupCount]:
                os.remove(old_file)


class LogConfig:
    """Lớp cấu hình logging cho ứng dụng"""
    
    def __init__(self, app_name, app_version, base_dir="Results"):
        """
        Khởi tạo cấu hình logging
        
        Args:
            app_name (str): Tên ứng dụng
            app_version (str): Phiên bản ứng dụng
            base_dir (str): Thư mục cơ sở để lưu log và kết quả
        """
        self.app_name = app_name
        self.app_version = app_version
        self.base_dir = base_dir
        self.timestamp = datetime.datetime.now().strftime('%d_%m_%H_%M_%S')
        
        # Tạo thư mục cơ sở nếu chưa tồn tại
        os.makedirs(base_dir, exist_ok=True)
        
        # Tạo thư mục log
        self.log_dir = os.path.join(base_dir, "logs")
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Tạo thư mục kết quả
        self.result_dir = os.path.join(base_dir, f"results")
        os.makedirs(self.result_dir, exist_ok=True)
        
        # Đường dẫn file log hệ thống
        self.system_log_file = os.path.join(self.log_dir, f"app_{self.timestamp}.log")
        
        # Đường dẫn các file kết quả
        self.result_files = {
            "chat_us": os.path.join(self.result_dir, "Chat_US.txt"),
            "checkpoint_2fa": os.path.join(self.result_dir, "Checkpoint_2FA.txt"),
            "checkpoint_0form": os.path.join(self.result_dir, "NoForm.txt"),
            "all_hit": os.path.join(self.result_dir, "all_hit.txt")
        }
        
        # Cấu hình các logger
        self.setup_system_logger()
        self.setup_result_loggers()
        
        # Log thông tin khởi động
        logging.info(f"Application {app_name} v{app_version} started")
        logging.info(f"Log directory: {self.log_dir}")
        logging.info(f"Result directory: {self.result_dir}")
    
    def setup_system_logger(self):
        """Cấu hình logger hệ thống"""
        # Tạo handler với rotation theo kích thước
        file_handler = logging.handlers.RotatingFileHandler(
            filename=self.system_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        
        console_handler = logging.StreamHandler()
        
        # Định dạng log
        formatter = logging.Formatter('%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Cấu hình root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # Xóa các handler cũ nếu có
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def setup_result_loggers(self):
        """Cấu hình các logger cho kết quả"""
        for name, file_path in self.result_files.items():
            self.setup_specific_result_logger(name, file_path)
    
    def setup_specific_result_logger(self, name, file_path):
        """
        Cấu hình logger cụ thể cho một loại kết quả
        
        Args:
            name (str): Tên logger
            file_path (str): Đường dẫn file log
        """
        logger = logging.getLogger(f"result.{name}")
        logger.setLevel(logging.INFO)
        logger.propagate = False
        
        # Xóa các handler cũ nếu có
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Tạo file handler với rotation khi file vượt quá 1GB
        file_handler = TimestampRotatingFileHandler(
            filename=file_path,
            maxBytes=512 * 1024 * 1024,  # 0.5 GB
            backupCount=20,
            encoding='utf-8'
        )
        
        # Định dạng đơn giản, chỉ hiển thị nội dung
        formatter = logging.Formatter('%(message)s')
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
    
    def get_result_logger(self, name):
        """
        Lấy logger cho một loại kết quả cụ thể
        
        Args:
            name (str): Tên logger
            
        Returns:
            logging.Logger: Logger tương ứng
        """
        return logging.getLogger(f"result.{name}")


# Hàm tiện ích để log kết quả
def log_2fa(uid, username, password, additional_info=None):
    """Log kết quả 2FA"""
    logger = logging.getLogger("result.checkpoint_2fa")
    if additional_info:
        logger.info(f"{uid}|{username}|{password}|{additional_info}")
    else:
        logger.info(f"{uid}|{username}|{password}")

def log_0form(uid, username, password, additional_info=None):
    """Log kết quả Confirm ID"""
    logger = logging.getLogger("result.checkpoint_0form")
    if additional_info:
        logger.info(f"{uid}|{username}|{password}|{additional_info}")
    else:
        logger.info(f"{uid}|{username}|{password}")

def log_chat_us(uid, username, password, additional_info=None):
    """Log kết quả Confirm ID"""
    logger = logging.getLogger("result.chat_us")
    if additional_info:
        logger.info(f"{uid}|{username}|{password}|{additional_info}")
    else:
        logger.info(f"{uid}|{username}|{password}")

def log_hit(uid, username, password, result_type, additional_info=None):
    """Log tất cả kết quả"""
    logger = logging.getLogger("result.all_hit")
    if additional_info:
        logger.info(f"{uid}|{username}|{password}|{result_type}|{additional_info}")
    else:
        logger.info(f"{uid}|{username}|{password}|{result_type}")
