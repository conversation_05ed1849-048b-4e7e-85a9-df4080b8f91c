#!/usr/bin/env python3
"""
Example API client for MFB Server
Demonstrates how to interact with the server via REST API
"""
import requests
import json
import time
from typing import Dict, List


class MFBClient:
    """Client for MFB Server API"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def get_server_status(self) -> Dict:
        """Get server status"""
        response = self.session.get(f"{self.base_url}/api/status")
        response.raise_for_status()
        return response.json()
    
    def get_tasks(self) -> List[Dict]:
        """Get all tasks"""
        response = self.session.get(f"{self.base_url}/api/tasks")
        response.raise_for_status()
        return response.json()["tasks"]
    
    def create_task(self, task_config: Dict) -> str:
        """Create new task"""
        response = self.session.post(f"{self.base_url}/api/tasks", json=task_config)
        response.raise_for_status()
        return response.json()["task_id"]
    
    def delete_task(self, task_id: str) -> bool:
        """Delete task"""
        response = self.session.delete(f"{self.base_url}/api/tasks/{task_id}")
        response.raise_for_status()
        return response.json()["success"]
    
    def start_task(self, task_id: str) -> bool:
        """Start task"""
        response = self.session.post(f"{self.base_url}/api/tasks/{task_id}/start")
        response.raise_for_status()
        return response.json()["success"]
    
    def stop_task(self, task_id: str) -> bool:
        """Stop task"""
        response = self.session.post(f"{self.base_url}/api/tasks/{task_id}/stop")
        response.raise_for_status()
        return response.json()["success"]
    
    def resume_task(self, task_id: str) -> bool:
        """Resume task"""
        response = self.session.post(f"{self.base_url}/api/tasks/{task_id}/resume")
        response.raise_for_status()
        return response.json()["success"]
    
    def get_task_status(self, task_id: str) -> Dict:
        """Get task status"""
        response = self.session.get(f"{self.base_url}/api/tasks/{task_id}/status")
        response.raise_for_status()
        return response.json()
    
    def start_all_tasks(self) -> Dict:
        """Start all tasks"""
        response = self.session.post(f"{self.base_url}/api/tasks/start-all")
        response.raise_for_status()
        return response.json()["results"]
    
    def stop_all_tasks(self) -> Dict:
        """Stop all tasks"""
        response = self.session.post(f"{self.base_url}/api/tasks/stop-all")
        response.raise_for_status()
        return response.json()["results"]


def demo_basic_operations():
    """Demo basic API operations"""
    print("\n=== Basic Operations Demo ===")
    
    client = MFBClient()
    
    try:
        # Get server status
        status = client.get_server_status()
        print(f"Server ID: {status['server_id']}")
        print(f"Version: {status['version']}")
        print(f"Running: {status['running']}")
        print(f"Total tasks: {status['statistics']['total_tasks']}")
        
        # Get tasks
        tasks = client.get_tasks()
        print(f"\nFound {len(tasks)} tasks:")
        for task in tasks:
            print(f"  - {task['id'][:8]}: {task['name']} ({task['file_path']})")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure server is running on http://localhost:5000")
    except Exception as ex:
        print(f"❌ Error: {ex}")


def demo_task_management():
    """Demo task creation and management"""
    print("\n=== Task Management Demo ===")
    
    client = MFBClient()
    
    try:
        # Create a new task
        task_config = {
            "name": "Demo Task",
            "file_path": "data/demo.txt",
            "max_workers": 3,
            "min_like": 100
        }
        
        print("Creating new task...")
        task_id = client.create_task(task_config)
        print(f"✅ Created task: {task_id}")
        
        # Get task status
        status = client.get_task_status(task_id)
        print(f"Task status: {status}")
        
        # Start task (will fail if file doesn't exist, but that's OK for demo)
        print("Attempting to start task...")
        try:
            success = client.start_task(task_id)
            print(f"Start result: {success}")
        except Exception as ex:
            print(f"Start failed (expected): {ex}")
        
        # Delete task
        print("Deleting task...")
        success = client.delete_task(task_id)
        print(f"✅ Delete result: {success}")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure server is running on http://localhost:5000")
    except Exception as ex:
        print(f"❌ Error: {ex}")


def demo_monitoring():
    """Demo real-time monitoring"""
    print("\n=== Monitoring Demo ===")
    
    client = MFBClient()
    
    try:
        print("Monitoring server for 30 seconds...")
        start_time = time.time()
        
        while time.time() - start_time < 30:
            status = client.get_server_status()
            stats = status['statistics']
            
            print(f"\r[{time.strftime('%H:%M:%S')}] "
                  f"Tasks: {stats['active_tasks']}/{stats['total_tasks']} | "
                  f"Processed: {stats['total_processed']:,} | "
                  f"2FA: {stats['total_2fa']:,} | "
                  f"Access: {stats['total_access']:,}", end='', flush=True)
            
            time.sleep(2)
        
        print("\n✅ Monitoring completed")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure server is running on http://localhost:5000")
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
    except Exception as ex:
        print(f"\n❌ Error: {ex}")


def demo_bulk_operations():
    """Demo bulk operations"""
    print("\n=== Bulk Operations Demo ===")
    
    client = MFBClient()
    
    try:
        # Get current tasks
        tasks = client.get_tasks()
        if not tasks:
            print("No tasks found. Create some tasks first.")
            return
        
        print(f"Found {len(tasks)} tasks")
        
        # Start all tasks
        print("Starting all tasks...")
        results = client.start_all_tasks()
        success_count = sum(1 for success in results.values() if success)
        print(f"✅ Started {success_count}/{len(results)} tasks")
        
        # Wait a bit
        time.sleep(5)
        
        # Stop all tasks
        print("Stopping all tasks...")
        results = client.stop_all_tasks()
        success_count = sum(1 for success in results.values() if success)
        print(f"✅ Stopped {success_count}/{len(results)} tasks")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure server is running on http://localhost:5000")
    except Exception as ex:
        print(f"❌ Error: {ex}")


def main():
    print("MFB API Client Demo")
    print("=" * 50)
    
    # Run demos
    demo_basic_operations()
    demo_task_management()
    demo_monitoring()
    demo_bulk_operations()
    
    print("\n" + "=" * 50)
    print("Demo completed!")
    print("\nTo start the server, run: python server_main.py")
    print("To create sample tasks, run: python server_main.py create-samples")


if __name__ == "__main__":
    main()
