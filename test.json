{"__ar": 1, "rid": "AjZ3Ip7VO4QojSKBpsPq2qb", "payload": {"layout": {"bloks_payload": {"data": [{"id": "1o8nm9n0hu", "type": "gs", "data": {"key": "CAA_LOGIN_FALLBACK:fallback_triggered", "mode": "d", "initial": false}}], "props": [{"id": "-6917714003244327204", "name": "ttrc_instance_id"}, {"id": "2231046902902521263", "name": "override_login_success_action"}, {"id": "-3430068907069239942", "name": "override_login_2fa_action"}], "error_attribution": {"logging_id": "{\"callsite\":\"{\\\"product\\\":\\\"bloks_unknown\\\",\\\"feature\\\":\\\"unknown\\\",\\\"oncall\\\":\\\"wbloks\\\"}\",\"push_phase\":\"C3\",\"version\":1,\"request_id\":\"AjZ3Ip7VO4QojSKBpsPq2qb\",\"www_revision\":1022958317}", "source_map_id": "2DHUix4K"}, "action": "(bk.action.core.TakeLast, (bk.action.core.FuncConst, 1), (bk.action.core.If, (bk.action.bloks.GetVariable2, \"1o8nm9n0hu\"), (bk.action.core.Apply, (bk.action.core.FuncConst, (bk.action.qpl.MarkerAnnotateV2, (bk.action.core.GetArg, 0), (bk.action.core.GetArg, 1), (bk.action.core.GetArg, 2), (bk.action.tree.Make, \"bk.data.qpl.EventConfig\"))), 2295576, 0, (bk.action.map.Make, (bk.action.array.Make, \"fallback_triggered\"), (bk.action.array.Make, true))), null), (bk.action.core.TakeLast, (bk.action.logging.LogEvent, \"caa_login_client_events_fb_msgr\", \"\", (bk.action.map.Make, (bk.action.array.Make, \"core\", \"login_params\"), (bk.action.array.Make, (bk.action.map.Make, (bk.action.array.Make, \"caa_core_data_encrypted\", \"event_category\", \"event_flow\", \"event_step\", \"event\", \"extra_client_data_bks_input\", \"is_dark_mode\", \"waterfall_id\", \"rl_client_session_id\", \"access_flow_version\", \"client_error_message\"), (bk.action.array.Make, \"AXekp-wX5OAAGIxcYm5dp9-30K9vENPeJj07MxxKz8sRx1kw1h4Od-Kd-Hn9o3ZOy9vlsSZwXyz18grxuP9ZOg0OacIpvsjkZpm94UXrerDf7JMqwVBncXUdCas7DxgMs2dtrJ375AjvtcveTTy2S3vx3alRqMsri22TJJRtdeUmvciz44M8cn_IzyINTqmf6__tRZh4KAjaP2h90nDAOXYNJyuboOuxsMKlmJkk7rPV1aisqgiFxMa1OjuXt6nxjioUJ2LjgM6gh0fPKfmMcrMHisyBIdlLFJXUmIxZFDEXBDSjlKwp7jyEfJw_l5_JxVMz3BrjUA4S4mp4WvcjOEVt0_hGnOhlVPdVCR84_MX3pURgy-t9-6zoLuyJ2gQPDR0SeTpZh7MG75iYe5bEwryd4JfltDDCPtjXUzh9aoX33cfLL636QQG0kpoO6WfRHdwtuOEAMZ52vAMkzsYc1PYhVX4dpCbye3avg7c3BsYO2U7YfaiQQMKa7a2BFYxZNdhOKV1EzfLikPDdf6wdFzKkxdHvvyqB6b2ZtTAS87hkVLkwhQ2hevlOl_y3avx9WXPQRHiLfWlEhKFOqc6RYUEbJlJFohIgDIraEmEkODX01s8JiEprNN1jiOJyIEyDXym3Hxd13KNScRv4k7wKc3QbgMdrsC6sUctpibSVYdv2Rg1r2ybO1yAO_A\", \"two_fac\", \"two_fac\", \"two_fac_redirect\", \"redirection_to_two_fac\", (bk.action.map.Make, (bk.action.array.Make), (bk.action.array.Make)), false, \"35928373-dc64-4252-aed3-4dc7acc3ccc8\", null, \"F2_FLOW\", null)), (bk.action.map.Make, (bk.action.array.Make, \"used_pre_unified_password\"), (bk.action.array.Make, null))))), (bk.action.core.TakeLast, (bk.action.qpl.MarkerAnnotateV2, 2295576, 0, (bk.action.map.Make, (bk.action.array.Make, \"login_type\", \"login_source\"), (bk.action.array.Make, \"Password\", \"Login\")), (bk.action.tree.Make, \"bk.data.qpl.EventConfig\", \"sampling_type\", 1, \"sample_rate\", 1)), (bk.action.qpl.MarkerAnnotateV2, 2295576, 0, (bk.action.map.Make, (bk.action.array.Make, \"end_point\"), (bk.action.array.Make, \"redirect_two_fac\")), (bk.action.tree.Make, \"bk.data.qpl.EventConfig\", \"sampling_type\", 1, \"sample_rate\", 1)), (bk.action.qpl.MarkerEndV3, 2295576, 0, 87, (bk.action.tree.Make, \"bk.data.qpl.EventConfig\", \"sampling_type\", 1, \"sample_rate\", 1), (bk.action.tree.Make, \"bk.data.qpl.MarkerParams\"))), (bk.action.cds.PushScreen, (bk.action.tree.Make, \"bk.data.screen.Screen\", \"loading_screen\", (bk.action.tree.Make, \"bk.cds.bottomsheet.Wrapper\", \"content\", (bk.action.tree.Make, \"bk.components.Flexbox\", \"children\", (bk.action.array.Make))), \"app_id\", \"com.bloks.www.two_step_verification.entrypoint\", \"tti_marker_id\", 719983200, \"screen_id\", \"s3t3ea:3\", \"ttrc_marker_id\", 719983200), (bk.action.tree.Make, \"bk.data.cds.PushScreenOptions\", \"props\", (bk.action.core.FuncConst, (bk.action.map.Make, (bk.action.array.Make), (bk.action.array.Make)))), (bk.action.map.Make, (bk.action.array.Make, \"params\"), (bk.action.array.Make, (bk.action.string.JsonEncode, (bk.action.map.Make, (bk.action.array.Make, \"server_params\", \"client_input_params\"), (bk.action.array.Make, (bk.action.map.Make, (bk.action.array.Make, \"two_step_verification_context\", \"flow_source\", \"INTERNAL_INFRA_screen_id\"), (bk.action.array.Make, \"ARFf-DhtOnN1HgzSBYgYx-66utbro5qBwuJ6k-p5MA-RoVETyVuu2XEKDr228ICQBzyoVcmChaPFG1dyVJesARK8BPRztxjYTeHrLXkuHgHzdH8Hok-LJvB9NDufDBWdmFTvzkMSV6qCNAsO7uCFkE1_e3TT0wvXBQk6-dF2ngwG21oMnftZyd7kh1YY7vQuhL3qhO7jXZEiUBQu-ROtvwd7qk7IP88B0UTPWIx8t5ziYsUKPV3HeBBWya9yzGYVKA2AgN3zLTm1ZbqCj34nJJRLElwZQtt4DadjkLzTfBdR9DRX24xxg9oqfNlzz6v6iUQ5END32cS8Oj7Auk669vzx6JJdRzSpk9O2fAeSUSUpIAz61OVFU2au5kvik0FhuSoeKtlTkw8DhCfEJWmc5mC34lDHoWcYqYhsWrQr43T-buCVhorOZa-N1tXGiTjRYKx0CL284aWQd7nqeaJKva6hiRWpauNtwI1-nYBHAsKcv-DFFw7VMTbr3CTF_UcsRHMPUj3LHeIvMndI8ixqi7sxBjAjBNvDP-5aro7V-9C8ZLBQ_BKoFcsbwraRfR3S3t6x2ylE9-3-FG1ir1WPEJ7cmeosrjSRxjCN7uiKTlOaJAoPH5ulNY8DmZZQ9Evwtyuhwnar8CguPvNPAZj43dMXNjYPYw\", \"two_factor_login\", \"s3t3ea:3\")), (bk.action.map.Make, (bk.action.array.Make, \"machine_id\", \"device_id\", \"is_whatsapp_installed\"), (bk.action.array.Make, \"\", \"\", false))))))), (bk.action.core.GetArg, 0)), (bk.action.map.Make, (bk.action.array.Make, \"should_dismiss_loading\", \"has_identification_error\"), (bk.action.array.Make, false, false))))"}}, "server_data": {"falco_log_policy_map": {"caa_login_client_events_fb_msgr": {"r": 1, "s": 1}}}}, "lid": "7506059331357713833"}