# MFB Server Mode Usage Guide

## 🚀 Overview

MFB Application hiện đã hỗ trợ **Server Mode** với khả năng quản lý nhiều task thông qua REST API. Điều này cho phép:

- ✅ Quản lý nhiều task đồng thời
- ✅ Start/Stop/Resume task qua API
- ✅ Monitoring real-time
- ✅ Queue persistence với dữ liệu lớn
- ✅ Web interface integration

## 📋 Modes

### 1. Legacy Mode (Single Task)
```bash
python main.py
```
- Chạy một task duy nhất từ config
- Tự động thoát khi hoàn thành
- Backward compatible

### 2. Server Mode (Multi Task)
```bash
python server_main.py
```
- Ch<PERSON>y như một server
- Quản lý nhiều task qua API
- Không tự động thoát

## 🔧 Server Mode Setup

### 1. Start Server
```bash
# Start server
python server_main.py

# Create sample tasks (optional)
python server_main.py create-samples
```

### 2. Server Info
```
Server ID: abc12345
API Server: http://localhost:5000
Tasks loaded: 2

API Endpoints:
  GET  /api/status           - Server status
  GET  /api/tasks            - List all tasks
  POST /api/tasks            - Create new task
  ...
```

## 📡 API Endpoints

### Server Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/status` | Server status & statistics |
| POST | `/api/server/start` | Start server mode |
| POST | `/api/server/stop` | Stop server mode |

### Task Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/tasks` | List all tasks |
| POST | `/api/tasks` | Create new task |
| DELETE | `/api/tasks/{id}` | Delete task |
| GET | `/api/tasks/{id}/status` | Task status |
| POST | `/api/tasks/{id}/start` | Start task |
| POST | `/api/tasks/{id}/stop` | Stop task |
| POST | `/api/tasks/{id}/resume` | Resume task |

### Bulk Operations
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/tasks/start-all` | Start all tasks |
| POST | `/api/tasks/stop-all` | Stop all tasks |

## 💻 Usage Examples

### 1. Using Python Client
```python
from api_client_example import MFBClient

client = MFBClient("http://localhost:5000")

# Get server status
status = client.get_server_status()
print(f"Server: {status['server_id']}")

# Create task
task_config = {
    "name": "My Task",
    "file_path": "data/accounts.txt",
    "max_workers": 5,
    "min_like": 100
}
task_id = client.create_task(task_config)

# Start task
client.start_task(task_id)

# Monitor task
status = client.get_task_status(task_id)
print(f"Queue: {status['queue_size']}")
```

### 2. Using cURL
```bash
# Get server status
curl http://localhost:5000/api/status

# Create task
curl -X POST http://localhost:5000/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Task",
    "file_path": "data/test.txt",
    "max_workers": 3,
    "min_like": 50
  }'

# Start task
curl -X POST http://localhost:5000/api/tasks/{task_id}/start

# Get task status
curl http://localhost:5000/api/tasks/{task_id}/status
```

## 🔄 Task Lifecycle

```
Create → Start → Running → Stop → Resume → Running → Complete
   ↓       ↓        ↓       ↓       ↓        ↓         ↓
 Config   Load    Process  Save   Load    Process   Cleanup
         Queue    Data    Queue   Queue    Data     Backup
```

### Task States
- **Created**: Task được tạo nhưng chưa chạy
- **Running**: Task đang xử lý dữ liệu
- **Stopped**: Task dừng, queue được lưu vào backup
- **Completed**: Task hoàn thành tất cả dữ liệu

## 💾 Queue Persistence

### Automatic Format Selection
```python
# Small dataset (< 1M items) → JSON format
{
  "task_id": "abc123",
  "queue_data": ["user1:pass1", "user2:pass2"],
  "statistics": {...}
}

# Large dataset (> 1M items) → Streaming format
# task_abc123_meta.json (metadata)
# task_abc123_data.txt (raw data)
```

### Performance
| Dataset Size | JSON Format | Streaming Format |
|-------------|-------------|------------------|
| 1M lines | ~3-5 seconds | ~0.5-1 second |
| 10M lines | ~30-60 seconds | ~3-5 seconds |
| 50M lines | ~5-10 minutes | ~15-30 seconds |

## 📊 Monitoring

### Real-time Statistics
```json
{
  "server_id": "abc12345",
  "statistics": {
    "total_tasks": 5,
    "active_tasks": 2,
    "total_processed": 15420,
    "total_2fa": 1250,
    "total_access": 89,
    "total_checkpoint": 234
  }
}
```

### Task Status
```json
{
  "id": "task-123",
  "name": "My Task",
  "running": true,
  "queue_size": 45230,
  "thread_count": 5,
  "cpm": 120,
  "elapsed_time": 3600
}
```

## 🛠️ Configuration

### Task Config
```json
{
  "name": "Task Name",
  "file_path": "path/to/data.txt",
  "max_workers": 5,
  "min_like": 100
}
```

### App Config (app_config.json)
```json
{
  "proxy": {
    "host": "proxy.example.com",
    "port": 8080,
    "username": "user",
    "password": "pass"
  },
  "max_workers": 10,
  "min_like": 500
}
```

## 🔧 Development

### Run Demo Client
```bash
python api_client_example.py
```

### File Structure
```
modules/
├── application.py      # Main application with task management
├── task.py            # Individual task implementation
├── queue_manager.py   # Queue persistence & optimization
├── api_server.py      # REST API server
└── ...

server_main.py         # Server mode entry point
api_client_example.py  # API client examples
```

## 🚨 Error Handling

### Common Issues
1. **Connection Error**: Server not running
2. **File Not Found**: Invalid file_path in task config
3. **Task Not Found**: Invalid task_id
4. **Already Running**: Task already started

### Logs
```
Results/logs/app_*.log  # Application logs
```

## 🎯 Best Practices

1. **Large Datasets**: Server tự động chọn streaming format
2. **Monitoring**: Sử dụng `/api/status` để theo dõi
3. **Graceful Shutdown**: Dùng Ctrl+C để dừng server
4. **Backup**: Queue tự động được backup khi stop
5. **Resume**: Sử dụng resume để tiếp tục từ backup

---

**Happy Processing! 🚀**
