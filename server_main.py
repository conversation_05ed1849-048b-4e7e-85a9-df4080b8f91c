#!/usr/bin/env python3
"""
Server mode main entry point
Chạy application ở chế độ server với API endpoints
"""
import sys
import signal
import time
import threading
from modules.application import Application
from modules.api_server import APIServer


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\nReceived shutdown signal. Stopping server...")
    global app, api_server
    
    if api_server:
        api_server.stop()
    
    if app:
        app.stop_server_mode()
    
    print("Server stopped.")
    sys.exit(0)


def main():
    global app, api_server
    
    print("=" * 60)
    print("MFB Server Mode")
    print("=" * 60)
    
    try:
        # Create application
        app = Application()
        
        # Start server mode
        app.start_server_mode()
        
        # Create and start API server
        api_server = APIServer(app)
        api_server.start(host='0.0.0.0', port=5000)
        
        print(f"Server started successfully!")
        print(f"Server ID: {app.server_id}")
        print(f"API Server: http://localhost:5000")
        print(f"Tasks loaded: {len(app.tasks)}")
        print("\nAPI Endpoints:")
        print("  GET  /api/status           - Server status")
        print("  GET  /api/tasks            - List all tasks")
        print("  POST /api/tasks            - Create new task")
        print("  GET  /api/tasks/{id}/status - Task status")
        print("  POST /api/tasks/{id}/start  - Start task")
        print("  POST /api/tasks/{id}/stop   - Stop task")
        print("  POST /api/tasks/{id}/resume - Resume task")
        print("  DELETE /api/tasks/{id}      - Delete task")
        print("\nPress Ctrl+C to stop the server")
        print("=" * 60)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Keep server running
        while app.running and app.server_mode:
            time.sleep(1)
            
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as ex:
        print(f"Error starting server: {ex}")
        sys.exit(1)


def create_sample_tasks():
    """Create some sample tasks for testing"""
    app = Application()
    
    # Sample task configurations
    sample_tasks = [
        {
            "name": "Sample Task 1",
            "file_path": "data/sample1.txt",
            "max_workers": 3,
            "min_like": 100
        },
        {
            "name": "Sample Task 2", 
            "file_path": "data/sample2.txt",
            "max_workers": 5,
            "min_like": 500
        }
    ]
    
    print("Creating sample tasks...")
    for task_config in sample_tasks:
        try:
            task_id = app.create_task(task_config)
            print(f"Created task: {task_id} - {task_config['name']}")
        except Exception as ex:
            print(f"Failed to create task {task_config['name']}: {ex}")
    
    print(f"Total tasks: {len(app.tasks)}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "create-samples":
        create_sample_tasks()
    else:
        main()
